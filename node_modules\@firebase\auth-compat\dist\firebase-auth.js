((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(qn,Bn){try{!function(){function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var F=t(qn);let V=()=>{},x={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(i,e){if(!Array.isArray(i))throw Error("encodeByteArray takes an array as a parameter");this.init_();var n=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,s=[];for(let r=0;r<i.length;r+=3){var a=i[r],o=r+1<i.length,c=o?i[r+1]:0,l=r+2<i.length,d=l?i[r+2]:0;let e=(15&c)<<2|d>>6,t=63&d;l||(t=64,o)||(e=64),s.push(n[a>>2],n[(3&a)<<4|c>>4],n[e],n[t])}return s.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray((r=>{var i=[];let n=0;for(let t=0;t<r.length;t++){let e=r.charCodeAt(t);e<128?i[n++]=e:(e<2048?i[n++]=e>>6|192:(55296==(64512&e)&&t+1<r.length&&56320==(64512&r.charCodeAt(t+1))?(e=65536+((1023&e)<<10)+(1023&r.charCodeAt(++t)),i[n++]=e>>18|240,i[n++]=e>>12&63|128):i[n++]=e>>12|224,i[n++]=e>>6&63|128),i[n++]=63&e|128)}return i})(e),t)},decodeString(r,i){if(this.HAS_NATIVE_SUPPORT&&!i)return atob(r);{var n=this.decodeStringToByteArray(r,i);var s=[];let e=0,t=0;for(;e<n.length;){var a,o,c,l=n[e++];l<128?s[t++]=String.fromCharCode(l):191<l&&l<224?(a=n[e++],s[t++]=String.fromCharCode((31&l)<<6|63&a)):239<l&&l<365?(a=((7&l)<<18|(63&n[e++])<<12|(63&n[e++])<<6|63&n[e++])-65536,s[t++]=String.fromCharCode(55296+(a>>10)),s[t++]=String.fromCharCode(56320+(1023&a))):(o=n[e++],c=n[e++],s[t++]=String.fromCharCode((15&l)<<12|(63&o)<<6|63&c))}return s.join("");return}},decodeStringToByteArray(t,e){this.init_();var r=e?this.charToByteMapWebSafe_:this.charToByteMap_,i=[];for(let e=0;e<t.length;){var n=r[t.charAt(e++)],s=e<t.length?r[t.charAt(e)]:0,a=++e<t.length?r[t.charAt(e)]:64,o=++e<t.length?r[t.charAt(e)]:64;if(++e,null==n||null==s||null==a||null==o)throw new H;i.push(n<<2|s>>4),64!==a&&(i.push(s<<4&240|a>>2),64!==o)&&i.push(a<<6&192|o)}return i},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),(this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e)>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class H extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let W=function(e){try{return x.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};let j=()=>(()=>{if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")})().__FIREBASE_DEFAULTS__,q=()=>{var e;return"undefined"!=typeof process&&void 0!==process.env&&(e=process.env.__FIREBASE_DEFAULTS__)?JSON.parse(e):void 0},B=()=>{if("undefined"!=typeof document){let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}var t=e&&W(e[1]);return t&&JSON.parse(t)}},z=()=>{try{return V()||j()||q()||B()}catch(e){console.info("Unable to get __FIREBASE_DEFAULTS__ due to: "+e)}};var i;function G(e){try{return(e.startsWith("http://")||e.startsWith("https://")?new URL(e).hostname:e).endsWith(".cloudworkstations.dev")}catch{}}let K={};let J=!1;function Y(e,t){if("undefined"!=typeof window&&"undefined"!=typeof document&&G(window.location.host)&&K[e]!==t&&!K[e]&&!J){K[e]=t;let c="__firebase__banner";let l=0<(()=>{var e,t={prod:[],emulator:[]};for(e of Object.keys(K))(K[e]?t.emulator:t.prod).push(e);return t})().prod.length;function d(e){return"__firebase__banner__"+e}function h(){var e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{var e;J=!0,(e=document.getElementById(c))&&e.remove()},e}function r(){var e,t=(e=>{let t=document.getElementById(e),r=!1;return t||((t=document.createElement("div")).setAttribute("id",e),r=!0),{created:r,element:t}})(c),r=d("text"),i=document.getElementById(r)||document.createElement("span"),n=d("learnmore"),s=document.getElementById(n)||document.createElement("a"),a=d("preprendIcon"),o=document.getElementById(a)||document.createElementNS("http://www.w3.org/2000/svg","svg");t.created&&(t=t.element,(e=t).style.display="flex",e.style.background="#7faaf0",e.style.position="fixed",e.style.bottom="5px",e.style.left="5px",e.style.padding=".5em",e.style.borderRadius="5px",e.style.alignItems="center",(e=s).setAttribute("id",n),e.innerText="Learn more",e.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",e.setAttribute("target","__blank"),e.style.paddingLeft="5px",e.style.textDecoration="underline",n=h(),e=a,(a=o).setAttribute("width","24"),a.setAttribute("id",e),a.setAttribute("height","24"),a.setAttribute("viewBox","0 0 24 24"),a.setAttribute("fill","none"),a.style.marginLeft="-6px",t.append(o,i,s,n),document.body.appendChild(t)),l?(i.innerText="Preview backend disconnected.",o.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(o.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,i.innerText="Preview backend running in this workspace."),i.setAttribute("id",r)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",r):r()}}function c(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function $(){var e=z()?.forceEnvironment;if("node"===e)return!0;if("browser"===e)return!1;try{return"[object process]"===Object.prototype.toString.call(global.process)}catch(e){return!1}}function X(){var e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}function Z(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function Q(){var e=c();return 0<=e.indexOf("MSIE ")||0<=e.indexOf("Trident/")}function ee(){try{return"object"==typeof indexedDB}catch(e){return!1}}class l extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,l.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,te.prototype.create)}}class te{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var i,t=t[0]||{},r=this.service+"/"+e,e=this.errors[e],e=e?(i=t,e.replace(re,(e,t)=>{var r=i[t];return null!=r?String(r):`<${t}?>`})):"Error",e=this.serviceName+`: ${e} (${r}).`;return new l(r,e,t)}}let re=/\{\$([^}]+)}/g;function ie(e,t){if(e!==t){var r,i,n=Object.keys(e),s=Object.keys(t);for(r of n){if(!s.includes(r))return!1;var a=e[r],o=t[r];if(ne(a)&&ne(o)){if(!ie(a,o))return!1}else if(a!==o)return!1}for(i of s)if(!n.includes(i))return!1}return!0}function ne(e){return null!==e&&"object"==typeof e}function se(r){let i=[];for(let[t,e]of Object.entries(r))Array.isArray(e)?e.forEach(e=>{i.push(encodeURIComponent(t)+"="+encodeURIComponent(e))}):i.push(encodeURIComponent(t)+"="+encodeURIComponent(e));return i.length?"&"+i.join("&"):""}function ae(e){let r={};return e.replace(/^\?/,"").split("&").forEach(e=>{var t;e&&([e,t]=e.split("="),r[decodeURIComponent(e)]=decodeURIComponent(t))}),r}function oe(e){var t,r=e.indexOf("?");return r?(t=e.indexOf("#",r),e.substring(r,0<t?t:void 0)):""}class ce{constructor(e,t){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=t,this.task.then(()=>{e(this)}).catch(e=>{this.error(e)})}next(t){this.forEachObserver(e=>{e.next(t)})}error(t){this.forEachObserver(e=>{e.error(t)}),this.close(t)}complete(){this.forEachObserver(e=>{e.complete()}),this.close()}subscribe(e,t,r){let i;if(void 0===e&&void 0===t&&void 0===r)throw new Error("Missing Observer.");void 0===(i=((e,t)=>{if("object"==typeof e&&null!==e)for(var r of t)if(r in e&&"function"==typeof e[r])return 1})(e,["next","error","complete"])?e:{next:e,error:t,complete:r}).next&&(i.next=le),void 0===i.error&&(i.error=le),void 0===i.complete&&(i.complete=le);e=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?i.error(this.finalError):i.complete()}catch(e){}}),this.observers.push(i),e}unsubscribeOne(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],--this.observerCount,0===this.observerCount)&&void 0!==this.onNoObservers&&this.onNoObservers(this)}forEachObserver(t){if(!this.finalized)for(let e=0;e<this.observers.length;e++)this.sendOne(e,t)}sendOne(e,t){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[e])try{t(this.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}})}close(e){this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}}function le(){}function s(e){return e&&e._delegate?e._delegate:e}(e=i=i||{})[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT";let de={debug:i.DEBUG,verbose:i.VERBOSE,info:i.INFO,warn:i.WARN,error:i.ERROR,silent:i.SILENT},he=i.INFO,ue={[i.DEBUG]:"log",[i.VERBOSE]:"log",[i.INFO]:"info",[i.WARN]:"warn",[i.ERROR]:"error"},pe=(e,t,...r)=>{if(!(t<e.logLevel)){var i=(new Date).toISOString(),n=ue[t];if(!n)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[n](`[${i}]  ${e.name}:`,...r)}};class me{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let ge={FACEBOOK:"facebook.com",GITHUB:"github.com",GOOGLE:"google.com",PASSWORD:"password",PHONE:"phone",TWITTER:"twitter.com"},fe={EMAIL_SIGNIN:"EMAIL_SIGNIN",PASSWORD_RESET:"PASSWORD_RESET",RECOVER_EMAIL:"RECOVER_EMAIL",REVERT_SECOND_FACTOR_ADDITION:"REVERT_SECOND_FACTOR_ADDITION",VERIFY_AND_CHANGE_EMAIL:"VERIFY_AND_CHANGE_EMAIL",VERIFY_EMAIL:"VERIFY_EMAIL"};function ve(){return{"dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK."}}function _e(){return{"admin-restricted-operation":"This operation is restricted to administrators only.","argument-error":"","app-not-authorized":"This app, identified by the domain where it's hosted, is not authorized to use Firebase Authentication with the provided API key. Review your key configuration in the Google API console.","app-not-installed":"The requested mobile application corresponding to the identifier (Android package name or iOS bundle ID) provided is not installed on this device.","captcha-check-failed":"The reCAPTCHA response token provided is either invalid, expired, already used or the domain associated with it does not match the list of whitelisted domains.","code-expired":"The SMS code has expired. Please re-send the verification code to try again.","cordova-not-ready":"Cordova framework is not ready.","cors-unsupported":"This browser is not supported.","credential-already-in-use":"This credential is already associated with a different user account.","custom-token-mismatch":"The custom token corresponds to a different audience.","requires-recent-login":"This operation is sensitive and requires recent authentication. Log in again before retrying this request.","dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK.","dynamic-link-not-activated":"Please activate Dynamic Links in the Firebase Console and agree to the terms and conditions.","email-change-needs-verification":"Multi-factor users must always have a verified email.","email-already-in-use":"The email address is already in use by another account.","emulator-config-failed":'Auth instance has already been used to make a network call. Auth can no longer be configured to use the emulator. Try calling "connectAuthEmulator()" sooner.',"expired-action-code":"The action code has expired.","cancelled-popup-request":"This operation has been cancelled due to another conflicting popup being opened.","internal-error":"An internal AuthError has occurred.","invalid-app-credential":"The phone verification request contains an invalid application verifier. The reCAPTCHA token response is either invalid or expired.","invalid-app-id":"The mobile app identifier is not registered for the current project.","invalid-user-token":"This user's credential isn't valid for this project. This can happen if the user's token has been tampered with, or if the user isn't for the project associated with this API key.","invalid-auth-event":"An internal AuthError has occurred.","invalid-verification-code":"The SMS verification code used to create the phone auth credential is invalid. Please resend the verification code sms and be sure to use the verification code provided by the user.","invalid-continue-uri":"The continue URL provided in the request is invalid.","invalid-cordova-configuration":"The following Cordova plugins must be installed to enable OAuth sign-in: cordova-plugin-buildinfo, cordova-universal-links-plugin, cordova-plugin-browsertab, cordova-plugin-inappbrowser and cordova-plugin-customurlscheme.","invalid-custom-token":"The custom token format is incorrect. Please check the documentation.","invalid-dynamic-link-domain":"The provided dynamic link domain is not configured or authorized for the current project.","invalid-email":"The email address is badly formatted.","invalid-emulator-scheme":"Emulator URL must start with a valid scheme (http:// or https://).","invalid-api-key":"Your API key is invalid, please check you have copied it correctly.","invalid-cert-hash":"The SHA-1 certificate hash provided is invalid.","invalid-credential":"The supplied auth credential is incorrect, malformed or has expired.","invalid-message-payload":"The email template corresponding to this action contains invalid characters in its message. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-multi-factor-session":"The request does not contain a valid proof of first factor successful sign-in.","invalid-oauth-provider":"EmailAuthProvider is not supported for this operation. This operation only supports OAuth providers.","invalid-oauth-client-id":"The OAuth client ID provided is either invalid or does not match the specified API key.","unauthorized-domain":"This domain is not authorized for OAuth operations for your Firebase project. Edit the list of authorized domains from the Firebase console.","invalid-action-code":"The action code is invalid. This can happen if the code is malformed, expired, or has already been used.","wrong-password":"The password is invalid or the user does not have a password.","invalid-persistence-type":"The specified persistence type is invalid. It can only be local, session or none.","invalid-phone-number":"The format of the phone number provided is incorrect. Please enter the phone number in a format that can be parsed into E.164 format. E.164 phone numbers are written in the format [+][country code][subscriber number including area code].","invalid-provider-id":"The specified provider ID is invalid.","invalid-recipient-email":"The email corresponding to this action failed to send as the provided recipient email address is invalid.","invalid-sender":"The email template corresponding to this action contains an invalid sender email or name. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-verification-id":"The verification ID used to create the phone auth credential is invalid.","invalid-tenant-id":"The Auth instance's tenant ID is invalid.","login-blocked":"Login blocked by user-provided method: {$originalMessage}","missing-android-pkg-name":"An Android Package Name must be provided if the Android App is required to be installed.","auth-domain-config-required":"Be sure to include authDomain when calling firebase.initializeApp(), by following the instructions in the Firebase console.","missing-app-credential":"The phone verification request is missing an application verifier assertion. A reCAPTCHA response token needs to be provided.","missing-verification-code":"The phone auth credential was created with an empty SMS verification code.","missing-continue-uri":"A continue URL must be provided in the request.","missing-iframe-start":"An internal AuthError has occurred.","missing-ios-bundle-id":"An iOS Bundle ID must be provided if an App Store ID is provided.","missing-or-invalid-nonce":"The request does not contain a valid nonce. This can occur if the SHA-256 hash of the provided raw nonce does not match the hashed nonce in the ID token payload.","missing-password":"A non-empty password must be provided","missing-multi-factor-info":"No second factor identifier is provided.","missing-multi-factor-session":"The request is missing proof of first factor successful sign-in.","missing-phone-number":"To send verification codes, provide a phone number for the recipient.","missing-verification-id":"The phone auth credential was created with an empty verification ID.","app-deleted":"This instance of FirebaseApp has been deleted.","multi-factor-info-not-found":"The user does not have a second factor matching the identifier provided.","multi-factor-auth-required":"Proof of ownership of a second factor is required to complete sign-in.","account-exists-with-different-credential":"An account already exists with the same email address but different sign-in credentials. Sign in using a provider associated with this email address.","network-request-failed":"A network AuthError (such as timeout, interrupted connection or unreachable host) has occurred.","no-auth-event":"An internal AuthError has occurred.","no-such-provider":"User was not linked to an account with the given provider.","null-user":"A null user object was provided as the argument for an operation which requires a non-null user object.","operation-not-allowed":"The given sign-in provider is disabled for this Firebase project. Enable it in the Firebase console, under the sign-in method tab of the Auth section.","operation-not-supported-in-this-environment":'This operation is not supported in the environment this application is running on. "location.protocol" must be http, https or chrome-extension and web storage must be enabled.',"popup-blocked":"Unable to establish a connection with the popup. It may have been blocked by the browser.","popup-closed-by-user":"The popup has been closed by the user before finalizing the operation.","provider-already-linked":"User can only be linked to one identity for the given provider.","quota-exceeded":"The project's quota for this operation has been exceeded.","redirect-cancelled-by-user":"The redirect operation has been cancelled by the user before finalizing.","redirect-operation-pending":"A redirect sign-in operation is already pending.","rejected-credential":"The request contains malformed or mismatching credentials.","second-factor-already-in-use":"The second factor is already enrolled on this account.","maximum-second-factor-count-exceeded":"The maximum allowed number of second factors on a user has been exceeded.","tenant-id-mismatch":"The provided tenant ID does not match the Auth instance's tenant ID",timeout:"The operation has timed out.","user-token-expired":"The user's credential is no longer valid. The user must sign in again.","too-many-requests":"We have blocked all requests from this device due to unusual activity. Try again later.","unauthorized-continue-uri":"The domain of the continue URL is not whitelisted.  Please whitelist the domain in the Firebase console.","unsupported-first-factor":"Enrolling a second factor or signing in with a multi-factor account requires sign-in with a supported first factor.","unsupported-persistence-type":"The current environment does not support the specified persistence type.","unsupported-tenant-operation":"This operation is not supported in a multi-tenant context.","unverified-email":"The operation requires a verified email.","user-cancelled":"The user did not grant your application the permissions it requested.","user-not-found":"There is no user record corresponding to this identifier. The user may have been deleted.","user-disabled":"The user account has been disabled by an administrator.","user-mismatch":"The supplied credentials do not correspond to the previously signed in user.","user-signed-out":"","weak-password":"The password must be 6 characters long or more.","web-storage-unsupported":"This browser is not supported or 3rd party cookies and data may be disabled.","already-initialized":"initializeAuth() has already been called with different options. To avoid this error, call initializeAuth() with the same options as when it was originally called, or call getAuth() to return the already initialized instance.","missing-recaptcha-token":"The reCAPTCHA token is missing when sending request to the backend.","invalid-recaptcha-token":"The reCAPTCHA token is invalid when sending request to the backend.","invalid-recaptcha-action":"The reCAPTCHA action is invalid when sending request to the backend.","recaptcha-not-enabled":"reCAPTCHA Enterprise integration is not enabled for this project.","missing-client-type":"The reCAPTCHA client type is missing when sending request to the backend.","missing-recaptcha-version":"The reCAPTCHA version is missing when sending request to the backend.","invalid-req-type":"Invalid request parameters.","invalid-recaptcha-version":"The reCAPTCHA version is invalid when sending request to the backend.","unsupported-password-policy-schema-version":"The password policy received from the backend uses a schema version that is not supported by this version of the Firebase SDK.","password-does-not-meet-requirements":"The password does not meet the requirements.","invalid-hosting-link-domain":"The provided Hosting link domain is not configured in Firebase Hosting or is not owned by the current project. This cannot be a default Hosting domain (`web.app` or `firebaseapp.com`)."}}let ye=ve,Ie=new te("auth","Firebase",ve()),we=new class{constructor(e){this.name=e,this._logLevel=he,this._logHandler=pe,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in i))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?de[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,i.DEBUG,...e),this._logHandler(this,i.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,i.VERBOSE,...e),this._logHandler(this,i.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,i.INFO,...e),this._logHandler(this,i.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,i.WARN,...e),this._logHandler(this,i.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,i.ERROR,...e),this._logHandler(this,i.ERROR,...e)}}("@firebase/auth");function Te(e,...t){we.logLevel<=i.ERROR&&we.error(`Auth (${Bn.SDK_VERSION}): `+e,...t)}function d(e,...t){throw ke(e,...t)}function h(e,...t){return ke(e,...t)}function Ee(e,t,r){r={...ye(),[t]:r};return new te("auth","Firebase",r).create(t,{appName:e.name})}function u(e){return Ee(e,"operation-not-supported-in-this-environment","Operations that alter the current user are not supported in conjunction with FirebaseServerApp")}function be(e,t,r){if(!(t instanceof r))throw r.name!==t.constructor.name&&d(e,"argument-error"),Ee(e,"argument-error",`Type of ${t.constructor.name} does not match expected instance.`+"Did you pass a reference from a different Auth SDK?")}function ke(e,...t){var r,i;return"string"!=typeof e?(r=t[0],(i=[...t.slice(1)])[0]&&(i[0].appName=e.name),e._errorFactory.create(r,...i)):Ie.create(e,...t)}function m(e,t,...r){if(!e)throw ke(t,...r)}function n(e){e="INTERNAL ASSERTION FAILED: "+e;throw Te(e),new Error(e)}function a(e,t){e||n(t)}function Se(){return"undefined"!=typeof self&&self.location?.href||""}function Re(){return"http:"===Ae()||"https:"===Ae()}function Ae(){return"undefined"!=typeof self&&self.location?.protocol||null}class Pe{constructor(e,t){a((this.shortDelay=e)<(this.longDelay=t),"Short delay should be less than long delay!"),this.isMobile="undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(c())||Z()}get(){return"undefined"!=typeof navigator&&navigator&&"onLine"in navigator&&"boolean"==typeof navigator.onLine&&(Re()||X()||"connection"in navigator)&&!navigator.onLine?Math.min(5e3,this.shortDelay):this.isMobile?this.longDelay:this.shortDelay}}function Ce(e,t){a(e.emulator,"Emulator should always be set here");e=e.emulator.url;return t?""+e+(t.startsWith("/")?t.slice(1):t):e}class Ne{static initialize(e,t,r){this.fetchImpl=e,t&&(this.headersImpl=t),r&&(this.responseImpl=r)}static fetch(){return this.fetchImpl||("undefined"!=typeof self&&"fetch"in self?self.fetch:"undefined"!=typeof globalThis&&globalThis.fetch?globalThis.fetch:"undefined"!=typeof fetch?fetch:void n("Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static headers(){return this.headersImpl||("undefined"!=typeof self&&"Headers"in self?self.Headers:"undefined"!=typeof globalThis&&globalThis.Headers?globalThis.Headers:"undefined"!=typeof Headers?Headers:void n("Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static response(){return this.responseImpl||("undefined"!=typeof self&&"Response"in self?self.Response:"undefined"!=typeof globalThis&&globalThis.Response?globalThis.Response:"undefined"!=typeof Response?Response:void n("Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}}let Oe={CREDENTIAL_MISMATCH:"custom-token-mismatch",MISSING_CUSTOM_TOKEN:"internal-error",INVALID_IDENTIFIER:"invalid-email",MISSING_CONTINUE_URI:"internal-error",INVALID_PASSWORD:"wrong-password",MISSING_PASSWORD:"missing-password",INVALID_LOGIN_CREDENTIALS:"invalid-credential",EMAIL_EXISTS:"email-already-in-use",PASSWORD_LOGIN_DISABLED:"operation-not-allowed",INVALID_IDP_RESPONSE:"invalid-credential",INVALID_PENDING_TOKEN:"invalid-credential",FEDERATED_USER_ID_ALREADY_LINKED:"credential-already-in-use",MISSING_REQ_TYPE:"internal-error",EMAIL_NOT_FOUND:"user-not-found",RESET_PASSWORD_EXCEED_LIMIT:"too-many-requests",EXPIRED_OOB_CODE:"expired-action-code",INVALID_OOB_CODE:"invalid-action-code",MISSING_OOB_CODE:"internal-error",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"requires-recent-login",INVALID_ID_TOKEN:"invalid-user-token",TOKEN_EXPIRED:"user-token-expired",USER_NOT_FOUND:"user-token-expired",TOO_MANY_ATTEMPTS_TRY_LATER:"too-many-requests",PASSWORD_DOES_NOT_MEET_REQUIREMENTS:"password-does-not-meet-requirements",INVALID_CODE:"invalid-verification-code",INVALID_SESSION_INFO:"invalid-verification-id",INVALID_TEMPORARY_PROOF:"invalid-credential",MISSING_SESSION_INFO:"missing-verification-id",SESSION_EXPIRED:"code-expired",MISSING_ANDROID_PACKAGE_NAME:"missing-android-pkg-name",UNAUTHORIZED_DOMAIN:"unauthorized-continue-uri",INVALID_OAUTH_CLIENT_ID:"invalid-oauth-client-id",ADMIN_ONLY_OPERATION:"admin-restricted-operation",INVALID_MFA_PENDING_CREDENTIAL:"invalid-multi-factor-session",MFA_ENROLLMENT_NOT_FOUND:"multi-factor-info-not-found",MISSING_MFA_ENROLLMENT_ID:"missing-multi-factor-info",MISSING_MFA_PENDING_CREDENTIAL:"missing-multi-factor-session",SECOND_FACTOR_EXISTS:"second-factor-already-in-use",SECOND_FACTOR_LIMIT_EXCEEDED:"maximum-second-factor-count-exceeded",BLOCKING_FUNCTION_ERROR_RESPONSE:"internal-error",RECAPTCHA_NOT_ENABLED:"recaptcha-not-enabled",MISSING_RECAPTCHA_TOKEN:"missing-recaptcha-token",INVALID_RECAPTCHA_TOKEN:"invalid-recaptcha-token",INVALID_RECAPTCHA_ACTION:"invalid-recaptcha-action",MISSING_CLIENT_TYPE:"missing-client-type",MISSING_RECAPTCHA_VERSION:"missing-recaptcha-version",INVALID_RECAPTCHA_VERSION:"invalid-recaptcha-version",INVALID_REQ_TYPE:"invalid-req-type"},Le=["/v1/accounts:signInWithCustomToken","/v1/accounts:signInWithEmailLink","/v1/accounts:signInWithIdp","/v1/accounts:signInWithPassword","/v1/accounts:signInWithPhoneNumber","/v1/token"],De=new Pe(3e4,6e4);function o(e,t){return e.tenantId&&!t.tenantId?{...t,tenantId:e.tenantId}:t}async function p(n,s,a,o,e={}){return Me(n,e,async()=>{let e={},t={};o&&("GET"===s?t=o:e={body:JSON.stringify(o)});var r=se({key:n.config.apiKey,...t}).slice(1),i=await n._getAdditionalHeaders(),i=(i["Content-Type"]="application/json",n.languageCode&&(i["X-Firebase-Locale"]=n.languageCode),{method:s,headers:i,...e});return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent||(i.referrerPolicy="no-referrer"),n.emulatorConfig&&G(n.emulatorConfig.host)&&(i.credentials="include"),Ne.fetch()(await Ue(n,n.config.apiHost,a,r),i)})}async function Me(t,e,r){t._canInitEmulator=!1;e={...Oe,...e};try{var i=new Fe(t),n=await Promise.race([r(),i.promise]),s=(i.clearNetworkTimeout(),await n.json());if("needConfirmation"in s)throw Ve(t,"account-exists-with-different-credential",s);if(n.ok&&!("errorMessage"in s))return s;var[a,o]=(n.ok?s.errorMessage:s.error.message).split(" : ");if("FEDERATED_USER_ID_ALREADY_LINKED"===a)throw Ve(t,"credential-already-in-use",s);if("EMAIL_EXISTS"===a)throw Ve(t,"email-already-in-use",s);if("USER_DISABLED"===a)throw Ve(t,"user-disabled",s);var c=e[a]||a.toLowerCase().replace(/[_\s]+/g,"-");if(o)throw Ee(t,c,o);d(t,c)}catch(e){if(e instanceof l)throw e;d(t,"network-request-failed",{message:String(e)})}}async function r(e,t,r,i,n={}){t=await p(e,t,r,i,n);return"mfaPendingCredential"in t&&d(e,"multi-factor-auth-required",{_serverResponse:t}),t}async function Ue(e,t,r,i){t=""+t+r+"?"+i,i=e,e=i.config.emulator?Ce(e.config,t):e.config.apiScheme+"://"+t;if(Le.includes(r)&&(await i._persistenceManagerAvailable,"COOKIE"===i._getPersistenceType()))return i._getPersistence()._getFinalTarget(e).toString();return e}class Fe{clearNetworkTimeout(){clearTimeout(this.timer)}constructor(e){this.auth=e,this.timer=null,this.promise=new Promise((e,t)=>{this.timer=setTimeout(()=>t(h(this.auth,"network-request-failed")),De.get())})}}function Ve(e,t,r){var i={appName:e.name},e=(r.email&&(i.email=r.email),r.phoneNumber&&(i.phoneNumber=r.phoneNumber),h(e,t,i));return e.customData._tokenResponse=r,e}function xe(e){return void 0!==e&&void 0!==e.getResponse}function He(e){return void 0!==e&&void 0!==e.enterprise}class We{constructor(e){if(this.siteKey="",this.recaptchaEnforcementState=[],void 0===e.recaptchaKey)throw new Error("recaptchaKey undefined");this.siteKey=e.recaptchaKey.split("/")[3],this.recaptchaEnforcementState=e.recaptchaEnforcementState}getProviderEnforcementState(t){if(this.recaptchaEnforcementState&&0!==this.recaptchaEnforcementState.length)for(let e of this.recaptchaEnforcementState)if(e.provider&&e.provider===t){switch(e.enforcementState){case"ENFORCE":return"ENFORCE";case"AUDIT":return"AUDIT";case"OFF":return"OFF";default:return"ENFORCEMENT_STATE_UNSPECIFIED"}return}return null}isProviderEnabled(e){return"ENFORCE"===this.getProviderEnforcementState(e)||"AUDIT"===this.getProviderEnforcementState(e)}isAnyProviderEnabled(){return this.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")||this.isProviderEnabled("PHONE_PROVIDER")}}async function je(e,t){return p(e,"GET","/v2/recaptchaConfig",o(e,t))}async function qe(e,t){return p(e,"POST","/v1/accounts:lookup",t)}function Be(e){if(e)try{var t=new Date(Number(e));if(!isNaN(t.getTime()))return t.toUTCString()}catch(e){}}function ze(e){return 1e3*Number(e)}function Ge(e){var[e,t,r]=e.split(".");if(void 0===e||void 0===t||void 0===r)return Te("JWT malformed, contained fewer than 3 sections"),null;try{var i=W(t);return i?JSON.parse(i):(Te("Failed to decode base64 JWT payload"),null)}catch(e){return Te("Caught error parsing JWT payload as JSON",e?.toString()),null}}function Ke(e){e=Ge(e);return m(e,"internal-error"),m(void 0!==e.exp,"internal-error"),m(void 0!==e.iat,"internal-error"),Number(e.exp)-Number(e.iat)}async function g(t,e,r=!1){if(r)return e;try{return await e}catch(e){throw e instanceof l&&(r=[e.code][0],"auth/user-disabled"===r||"auth/user-token-expired"===r)&&(t.auth.currentUser===t&&await t.auth.signOut()),e}}class Je{constructor(e){this.user=e,this.isRunning=!1,this.timerId=null,this.errorBackoff=3e4}_start(){this.isRunning||(this.isRunning=!0,this.schedule())}_stop(){this.isRunning&&(this.isRunning=!1,null!==this.timerId)&&clearTimeout(this.timerId)}getInterval(e){return e?(e=this.errorBackoff,this.errorBackoff=Math.min(2*this.errorBackoff,96e4),e):(this.errorBackoff=3e4,e=(this.user.stsTokenManager.expirationTime??0)-Date.now()-3e5,Math.max(0,e))}schedule(e=!1){this.isRunning&&(e=this.getInterval(e),this.timerId=setTimeout(async()=>{await this.iteration()},e))}async iteration(){try{await this.user.getIdToken(!0)}catch(e){return void("auth/network-request-failed"===e?.code&&this.schedule(!0))}this.schedule()}}class Ye{constructor(e,t){this.createdAt=e,this.lastLoginAt=t,this._initializeTime()}_initializeTime(){this.lastSignInTime=Be(this.lastLoginAt),this.creationTime=Be(this.createdAt)}_copy(e){this.createdAt=e.createdAt,this.lastLoginAt=e.lastLoginAt,this._initializeTime()}toJSON(){return{createdAt:this.createdAt,lastLoginAt:this.lastLoginAt}}}async function $e(e){var t=e.auth,r=await e.getIdToken(),r=await g(e,qe(t,{idToken:r})),t=(m(r?.users.length,t,"internal-error"),r.users[0]),r=(e._notifyReloadListener(t),t.providerUserInfo?.length?Xe(t.providerUserInfo):[]),r=((e,r)=>[...e=e.filter(t=>!r.some(e=>e.providerId===t.providerId)),...r])(e.providerData,r),i=!(e.email&&t.passwordHash||r?.length),i=!!e.isAnonymous&&i,r={uid:t.localId,displayName:t.displayName||null,photoURL:t.photoUrl||null,email:t.email||null,emailVerified:t.emailVerified||!1,phoneNumber:t.phoneNumber||null,tenantId:t.tenantId||null,providerData:r,metadata:new Ye(t.createdAt,t.lastLoginAt),isAnonymous:i};Object.assign(e,r)}function Xe(e){return e.map(({providerId:e,...t})=>({providerId:e,uid:t.rawId||"",displayName:t.displayName||null,email:t.email||null,phoneNumber:t.phoneNumber||null,photoURL:t.photoUrl||null}))}class Ze{constructor(){this.refreshToken=null,this.accessToken=null,this.expirationTime=null}get isExpired(){return!this.expirationTime||Date.now()>this.expirationTime-3e4}updateFromServerResponse(e){m(e.idToken,"internal-error"),m(void 0!==e.idToken,"internal-error"),m(void 0!==e.refreshToken,"internal-error");var t="expiresIn"in e&&void 0!==e.expiresIn?Number(e.expiresIn):Ke(e.idToken);this.updateTokensAndExpiration(e.idToken,e.refreshToken,t)}updateFromIdToken(e){m(0!==e.length,"internal-error");var t=Ke(e);this.updateTokensAndExpiration(e,null,t)}async getToken(e,t=!1){return t||!this.accessToken||this.isExpired?(m(this.refreshToken,e,"user-token-expired"),this.refreshToken?(await this.refresh(e,this.refreshToken),this.accessToken):null):this.accessToken}clearRefreshToken(){this.refreshToken=null}async refresh(e,t){n=t;var i,n,{accessToken:e,refreshToken:t,expiresIn:r}=await{accessToken:(t=await Me(i=e,{},async()=>{var e=se({grant_type:"refresh_token",refresh_token:n}).slice(1),{tokenApiHost:t,apiKey:r}=i.config,t=await Ue(i,t,"/v1/token","key="+r),r=await i._getAdditionalHeaders(),r=(r["Content-Type"]="application/x-www-form-urlencoded",{method:"POST",headers:r,body:e});return i.emulatorConfig&&G(i.emulatorConfig.host)&&(r.credentials="include"),Ne.fetch()(t,r)})).access_token,expiresIn:t.expires_in,refreshToken:t.refresh_token};this.updateTokensAndExpiration(e,t,Number(r))}updateTokensAndExpiration(e,t,r){this.refreshToken=t||null,this.accessToken=e||null,this.expirationTime=Date.now()+1e3*r}static fromJSON(e,t){var{refreshToken:t,accessToken:r,expirationTime:i}=t,n=new Ze;return t&&(m("string"==typeof t,"internal-error",{appName:e}),n.refreshToken=t),r&&(m("string"==typeof r,"internal-error",{appName:e}),n.accessToken=r),i&&(m("number"==typeof i,"internal-error",{appName:e}),n.expirationTime=i),n}toJSON(){return{refreshToken:this.refreshToken,accessToken:this.accessToken,expirationTime:this.expirationTime}}_assign(e){this.accessToken=e.accessToken,this.refreshToken=e.refreshToken,this.expirationTime=e.expirationTime}_clone(){return Object.assign(new Ze,this.toJSON())}_performRefresh(){return n("not implemented")}}function f(e,t){m("string"==typeof e||void 0===e,"internal-error",{appName:t})}class v{constructor({uid:e,auth:t,stsTokenManager:r,...i}){this.providerId="firebase",this.proactiveRefresh=new Je(this),this.reloadUserInfo=null,this.reloadListener=null,this.uid=e,this.auth=t,this.stsTokenManager=r,this.accessToken=r.accessToken,this.displayName=i.displayName||null,this.email=i.email||null,this.emailVerified=i.emailVerified||!1,this.phoneNumber=i.phoneNumber||null,this.photoURL=i.photoURL||null,this.isAnonymous=i.isAnonymous||!1,this.tenantId=i.tenantId||null,this.providerData=i.providerData?[...i.providerData]:[],this.metadata=new Ye(i.createdAt||void 0,i.lastLoginAt||void 0)}async getIdToken(e){e=await g(this,this.stsTokenManager.getToken(this.auth,e));return m(e,this.auth,"internal-error"),this.accessToken!==e&&(this.accessToken=e,await this.auth._persistUserIfCurrent(this),this.auth._notifyListenersIfCurrent(this)),e}getIdTokenResult(e){return(async(e,t=!1)=>{var e=s(e),r=Ge(t=await e.getIdToken(t)),i=(m(r&&r.exp&&r.auth_time&&r.iat,e.auth,"internal-error"),(e="object"==typeof r.firebase?r.firebase:void 0)?.sign_in_provider);return{claims:r,token:t,authTime:Be(ze(r.auth_time)),issuedAtTime:Be(ze(r.iat)),expirationTime:Be(ze(r.exp)),signInProvider:i||null,signInSecondFactor:e?.sign_in_second_factor||null}})(this,e)}reload(){return(async e=>{await $e(e=s(e)),await e.auth._persistUserIfCurrent(e),e.auth._notifyListenersIfCurrent(e)})(this)}_assign(e){this!==e&&(m(this.uid===e.uid,this.auth,"internal-error"),this.displayName=e.displayName,this.photoURL=e.photoURL,this.email=e.email,this.emailVerified=e.emailVerified,this.phoneNumber=e.phoneNumber,this.isAnonymous=e.isAnonymous,this.tenantId=e.tenantId,this.providerData=e.providerData.map(e=>({...e})),this.metadata._copy(e.metadata),this.stsTokenManager._assign(e.stsTokenManager))}_clone(e){e=new v({...this,auth:e,stsTokenManager:this.stsTokenManager._clone()});return e.metadata._copy(this.metadata),e}_onReload(e){m(!this.reloadListener,this.auth,"internal-error"),this.reloadListener=e,this.reloadUserInfo&&(this._notifyReloadListener(this.reloadUserInfo),this.reloadUserInfo=null)}_notifyReloadListener(e){this.reloadListener?this.reloadListener(e):this.reloadUserInfo=e}_startProactiveRefresh(){this.proactiveRefresh._start()}_stopProactiveRefresh(){this.proactiveRefresh._stop()}async _updateTokensIfNecessary(e,t=!1){let r=!1;e.idToken&&e.idToken!==this.stsTokenManager.accessToken&&(this.stsTokenManager.updateFromServerResponse(e),r=!0),t&&await $e(this),await this.auth._persistUserIfCurrent(this),r&&this.auth._notifyListenersIfCurrent(this)}async delete(){var e;return Bn._isFirebaseServerApp(this.auth.app)?Promise.reject(u(this.auth)):(e=await this.getIdToken(),await g(this,(async(e,t)=>p(e,"POST","/v1/accounts:delete",t))(this.auth,{idToken:e})),this.stsTokenManager.clearRefreshToken(),this.auth.signOut())}toJSON(){return{uid:this.uid,email:this.email||void 0,emailVerified:this.emailVerified,displayName:this.displayName||void 0,isAnonymous:this.isAnonymous,photoURL:this.photoURL||void 0,phoneNumber:this.phoneNumber||void 0,tenantId:this.tenantId||void 0,providerData:this.providerData.map(e=>({...e})),stsTokenManager:this.stsTokenManager.toJSON(),_redirectEventId:this._redirectEventId,...this.metadata.toJSON(),apiKey:this.auth.config.apiKey,appName:this.auth.name}}get refreshToken(){return this.stsTokenManager.refreshToken||""}static _fromJSON(e,t){var r=t.displayName??void 0,i=t.email??void 0,n=t.phoneNumber??void 0,s=t.photoURL??void 0,a=t.tenantId??void 0,o=t._redirectEventId??void 0,c=t.createdAt??void 0,l=t.lastLoginAt??void 0,{uid:t,emailVerified:d,isAnonymous:h,providerData:u,stsTokenManager:p}=t,p=(m(t&&p,e,"internal-error"),Ze.fromJSON(this.name,p)),t=(m("string"==typeof t,e,"internal-error"),f(r,e.name),f(i,e.name),m("boolean"==typeof d,e,"internal-error"),m("boolean"==typeof h,e,"internal-error"),f(n,e.name),f(s,e.name),f(a,e.name),f(o,e.name),f(c,e.name),f(l,e.name),new v({uid:t,auth:e,email:i,emailVerified:d,displayName:r,isAnonymous:h,photoURL:s,phoneNumber:n,tenantId:a,stsTokenManager:p,createdAt:c,lastLoginAt:l}));return u&&Array.isArray(u)&&(t.providerData=u.map(e=>({...e}))),o&&(t._redirectEventId=o),t}static async _fromIdTokenResponse(e,t,r=!1){var i=new Ze,t=(i.updateFromServerResponse(t),new v({uid:t.localId,auth:e,stsTokenManager:i,isAnonymous:r}));return await $e(t),t}static async _fromGetAccountInfoResponse(e,t,r){var t=t.users[0],i=(m(void 0!==t.localId,"internal-error"),void 0!==t.providerUserInfo?Xe(t.providerUserInfo):[]),n=!(t.email&&t.passwordHash||i?.length),s=new Ze,r=(s.updateFromIdToken(r),new v({uid:t.localId,auth:e,stsTokenManager:s,isAnonymous:n})),e={uid:t.localId,displayName:t.displayName||null,photoURL:t.photoUrl||null,email:t.email||null,emailVerified:t.emailVerified||!1,phoneNumber:t.phoneNumber||null,tenantId:t.tenantId||null,providerData:i,metadata:new Ye(t.createdAt,t.lastLoginAt),isAnonymous:!(t.email&&t.passwordHash||i?.length)};return Object.assign(r,e),r}}let Qe=new Map;function _(e){a(e instanceof Function,"Expected a class definition");var t=Qe.get(e);return t?a(t instanceof e,"Instance stored in cache mismatched with class"):(t=new e,Qe.set(e,t)),t}class et{constructor(){this.type="NONE",this.storage={}}async _isAvailable(){return!0}async _set(e,t){this.storage[e]=t}async _get(e){e=this.storage[e];return void 0===e?null:e}async _remove(e){delete this.storage[e]}_addListener(e,t){}_removeListener(e,t){}}et.type="NONE";let tt=et;function y(e,t,r){return`firebase:${e}:${t}:`+r}class rt{constructor(e,t,r){this.persistence=e,this.auth=t,this.userKey=r;var{config:e,name:r}=this.auth;this.fullUserKey=y(this.userKey,e.apiKey,r),this.fullPersistenceKey=y("persistence",e.apiKey,r),this.boundEventHandler=t._onStorageEvent.bind(t),this.persistence._addListener(this.fullUserKey,this.boundEventHandler)}setCurrentUser(e){return this.persistence._set(this.fullUserKey,e.toJSON())}async getCurrentUser(){var e,t=await this.persistence._get(this.fullUserKey);return t?"string"==typeof t?(e=await qe(this.auth,{idToken:t}).catch(()=>{}))?v._fromGetAccountInfoResponse(this.auth,e,t):null:v._fromJSON(this.auth,t):null}removeCurrentUser(){return this.persistence._remove(this.fullUserKey)}savePersistenceForRedirect(){return this.persistence._set(this.fullPersistenceKey,this.persistence.type)}async setPersistence(e){var t;if(this.persistence!==e)return t=await this.getCurrentUser(),await this.removeCurrentUser(),this.persistence=e,t?this.setCurrentUser(t):void 0}delete(){this.persistence._removeListener(this.fullUserKey,this.boundEventHandler)}static async create(t,e,r="authUser"){if(!e.length)return new rt(_(tt),t,r);var i,n=(await Promise.all(e.map(async e=>{if(await e._isAvailable())return e}))).filter(e=>e);let s=n[0]||_(tt),a=y(r,t.config.apiKey,t.name),o=null;for(i of e)try{var c=await i._get(a);if(c){let e;if("string"==typeof c){var l=await qe(t,{idToken:c}).catch(()=>{});if(!l)break;e=await v._fromGetAccountInfoResponse(t,l,c)}else e=v._fromJSON(t,c);i!==s&&(o=e),s=i;break}}catch{}n=n.filter(e=>e._shouldAllowMigration);return s._shouldAllowMigration&&n.length&&(s=n[0],o&&await s._set(a,o.toJSON()),await Promise.all(e.map(async e=>{if(e!==s)try{await e._remove(a)}catch{}}))),new rt(s,t,r)}}function it(e){var t=e.toLowerCase();return t.includes("opera/")||t.includes("opr/")||t.includes("opios/")?"Opera":ot(t)?"IEMobile":t.includes("msie")||t.includes("trident/")?"IE":t.includes("edge/")?"Edge":nt(t)?"Firefox":t.includes("silk/")?"Silk":lt(t)?"Blackberry":dt(t)?"Webos":st(t)?"Safari":!t.includes("chrome/")&&!at(t)||t.includes("edge/")?ct(t)?"Android":2===(t=e.match(/([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/))?.length?t[1]:"Other":"Chrome"}function nt(e=c()){return/firefox\//i.test(e)}function st(e=c()){e=e.toLowerCase();return e.includes("safari/")&&!e.includes("chrome/")&&!e.includes("crios/")&&!e.includes("android")}function at(e=c()){return/crios\//i.test(e)}function ot(e=c()){return/iemobile/i.test(e)}function ct(e=c()){return/android/i.test(e)}function lt(e=c()){return/blackberry/i.test(e)}function dt(e=c()){return/webos/i.test(e)}function ht(e=c()){return/iphone|ipad|ipod/i.test(e)||/macintosh/i.test(e)&&/mobile/i.test(e)}function ut(e=c()){return ht(e)||ct(e)||dt(e)||lt(e)||/windows phone/i.test(e)||ot(e)}function pt(e,t=[]){let r;switch(e){case"Browser":r=it(c());break;case"Worker":r=it(c())+"-"+e;break;default:r=e}t=t.length?t.join(","):"FirebaseCore-web";return`${r}/JsCore/${Bn.SDK_VERSION}/`+t}class mt{constructor(e){this.auth=e,this.queue=[]}pushCallback(i,e){var t=r=>new Promise((e,t)=>{try{e(i(r))}catch(e){t(e)}});t.onAbort=e,this.queue.push(t);let r=this.queue.length-1;return()=>{this.queue[r]=()=>Promise.resolve()}}async runMiddleware(e){if(this.auth.currentUser!==e){var t=[];try{for(var r of this.queue)await r(e),r.onAbort&&t.push(r.onAbort)}catch(e){t.reverse();for(var i of t)try{i()}catch(e){}throw this.auth._errorFactory.create("login-blocked",{originalMessage:e?.message})}}}}class gt{constructor(e){var t=e.customStrengthOptions;this.customStrengthOptions={},this.customStrengthOptions.minPasswordLength=t.minPasswordLength??6,t.maxPasswordLength&&(this.customStrengthOptions.maxPasswordLength=t.maxPasswordLength),void 0!==t.containsLowercaseCharacter&&(this.customStrengthOptions.containsLowercaseLetter=t.containsLowercaseCharacter),void 0!==t.containsUppercaseCharacter&&(this.customStrengthOptions.containsUppercaseLetter=t.containsUppercaseCharacter),void 0!==t.containsNumericCharacter&&(this.customStrengthOptions.containsNumericCharacter=t.containsNumericCharacter),void 0!==t.containsNonAlphanumericCharacter&&(this.customStrengthOptions.containsNonAlphanumericCharacter=t.containsNonAlphanumericCharacter),this.enforcementState=e.enforcementState,"ENFORCEMENT_STATE_UNSPECIFIED"===this.enforcementState&&(this.enforcementState="OFF"),this.allowedNonAlphanumericCharacters=e.allowedNonAlphanumericCharacters?.join("")??"",this.forceUpgradeOnSignin=e.forceUpgradeOnSignin??!1,this.schemaVersion=e.schemaVersion}validatePassword(e){var t={isValid:!0,passwordPolicy:this};return this.validatePasswordLengthOptions(e,t),this.validatePasswordCharacterOptions(e,t),t.isValid&&(t.isValid=t.meetsMinPasswordLength??!0),t.isValid&&(t.isValid=t.meetsMaxPasswordLength??!0),t.isValid&&(t.isValid=t.containsLowercaseLetter??!0),t.isValid&&(t.isValid=t.containsUppercaseLetter??!0),t.isValid&&(t.isValid=t.containsNumericCharacter??!0),t.isValid&&(t.isValid=t.containsNonAlphanumericCharacter??!0),t}validatePasswordLengthOptions(e,t){var r=this.customStrengthOptions.minPasswordLength,i=this.customStrengthOptions.maxPasswordLength;r&&(t.meetsMinPasswordLength=e.length>=r),i&&(t.meetsMaxPasswordLength=e.length<=i)}validatePasswordCharacterOptions(t,r){var i;this.updatePasswordCharacterOptionsStatuses(r,!1,!1,!1,!1);for(let e=0;e<t.length;e++)i=t.charAt(e),this.updatePasswordCharacterOptionsStatuses(r,"a"<=i&&i<="z","A"<=i&&i<="Z","0"<=i&&i<="9",this.allowedNonAlphanumericCharacters.includes(i))}updatePasswordCharacterOptionsStatuses(e,t,r,i,n){this.customStrengthOptions.containsLowercaseLetter&&!e.containsLowercaseLetter&&(e.containsLowercaseLetter=t),this.customStrengthOptions.containsUppercaseLetter&&!e.containsUppercaseLetter&&(e.containsUppercaseLetter=r),this.customStrengthOptions.containsNumericCharacter&&!e.containsNumericCharacter&&(e.containsNumericCharacter=i),this.customStrengthOptions.containsNonAlphanumericCharacter&&!e.containsNonAlphanumericCharacter&&(e.containsNonAlphanumericCharacter=n)}}class ft{constructor(e,t,r,i){this.app=e,this.heartbeatServiceProvider=t,this.appCheckServiceProvider=r,this.config=i,this.currentUser=null,this.emulatorConfig=null,this.operations=Promise.resolve(),this.authStateSubscription=new vt(this),this.idTokenSubscription=new vt(this),this.beforeStateQueue=new mt(this),this.redirectUser=null,this.isProactiveRefreshEnabled=!1,this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION=1,this._canInitEmulator=!0,this._isInitialized=!1,this._deleted=!1,this._initializationPromise=null,this._popupRedirectResolver=null,this._errorFactory=Ie,this._agentRecaptchaConfig=null,this._tenantRecaptchaConfigs={},this._projectPasswordPolicy=null,this._tenantPasswordPolicies={},this._resolvePersistenceManagerAvailable=void 0,this.lastNotifiedUid=void 0,this.languageCode=null,this.tenantId=null,this.settings={appVerificationDisabledForTesting:!1},this.frameworks=[],this.name=e.name,this.clientVersion=i.sdkClientVersion,this._persistenceManagerAvailable=new Promise(e=>this._resolvePersistenceManagerAvailable=e)}_initializeWithPersistence(e,t){return t&&(this._popupRedirectResolver=_(t)),this._initializationPromise=this.queue(async()=>{if(!this._deleted&&(this.persistenceManager=await rt.create(this,e),this._resolvePersistenceManagerAvailable?.(),!this._deleted)){if(this._popupRedirectResolver?._shouldInitProactively)try{await this._popupRedirectResolver._initialize(this)}catch(e){}await this.initializeCurrentUser(t),this.lastNotifiedUid=this.currentUser?.uid||null,this._deleted||(this._isInitialized=!0)}}),this._initializationPromise}async _onStorageEvent(){var e;!this._deleted&&(e=await this.assertedPersistence.getCurrentUser(),this.currentUser||e)&&(this.currentUser&&e&&this.currentUser.uid===e.uid?(this._currentUser._assign(e),await this.currentUser.getIdToken()):await this._updateCurrentUser(e,!0))}async initializeCurrentUserFromIdToken(e){try{var t=await qe(this,{idToken:e}),r=await v._fromGetAccountInfoResponse(this,t,e);await this.directlySetCurrentUser(r)}catch(e){console.warn("FirebaseServerApp could not login user with provided authIdToken: ",e),await this.directlySetCurrentUser(null)}}async initializeCurrentUser(e){if(Bn._isFirebaseServerApp(this.app)){let t=this.app.settings.authIdToken;return t?new Promise(e=>{setTimeout(()=>this.initializeCurrentUserFromIdToken(t).then(e,e))}):this.directlySetCurrentUser(null)}var t,r,i=await this.assertedPersistence.getCurrentUser();let n=i,s=!1;if(e&&this.config.authDomain&&(await this.getOrInitRedirectPersistenceManager(),t=this.redirectUser?._redirectEventId,r=n?._redirectEventId,e=await this.tryRedirectSignIn(e),t&&t!==r||!e?.user||(n=e.user,s=!0)),!n)return this.directlySetCurrentUser(null);if(n._redirectEventId)return m(this._popupRedirectResolver,this,"argument-error"),await this.getOrInitRedirectPersistenceManager(),this.redirectUser&&this.redirectUser._redirectEventId===n._redirectEventId?this.directlySetCurrentUser(n):this.reloadAndSetCurrentUserOrClear(n);if(s)try{await this.beforeStateQueue.runMiddleware(n)}catch(e){n=i,this._popupRedirectResolver._overrideRedirectResult(this,()=>Promise.reject(e))}return n?this.reloadAndSetCurrentUserOrClear(n):this.directlySetCurrentUser(null)}async tryRedirectSignIn(e){let t=null;try{t=await this._popupRedirectResolver._completeRedirectFn(this,e,!0)}catch(e){await this._setRedirectUser(null)}return t}async reloadAndSetCurrentUserOrClear(e){try{await $e(e)}catch(e){if("auth/network-request-failed"!==e?.code)return this.directlySetCurrentUser(null)}return this.directlySetCurrentUser(e)}useDeviceLanguage(){var e;this.languageCode="undefined"!=typeof navigator&&((e=navigator).languages&&e.languages[0]||e.language)||null}async _delete(){this._deleted=!0}async updateCurrentUser(e){return Bn._isFirebaseServerApp(this.app)?Promise.reject(u(this)):((e=e?s(e):null)&&m(e.auth.config.apiKey===this.config.apiKey,this,"invalid-user-token"),this._updateCurrentUser(e&&e._clone(this)))}async _updateCurrentUser(e,t=!1){if(!this._deleted)return e&&m(this.tenantId===e.tenantId,this,"tenant-id-mismatch"),t||await this.beforeStateQueue.runMiddleware(e),this.queue(async()=>{await this.directlySetCurrentUser(e),this.notifyAuthListeners()})}async signOut(){return Bn._isFirebaseServerApp(this.app)?Promise.reject(u(this)):(await this.beforeStateQueue.runMiddleware(null),(this.redirectPersistenceManager||this._popupRedirectResolver)&&await this._setRedirectUser(null),this._updateCurrentUser(null,!0))}setPersistence(e){return Bn._isFirebaseServerApp(this.app)?Promise.reject(u(this)):this.queue(async()=>{await this.assertedPersistence.setPersistence(_(e))})}_getRecaptchaConfig(){return null==this.tenantId?this._agentRecaptchaConfig:this._tenantRecaptchaConfigs[this.tenantId]}async validatePassword(e){this._getPasswordPolicyInternal()||await this._updatePasswordPolicy();var t=this._getPasswordPolicyInternal();return t.schemaVersion!==this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION?Promise.reject(this._errorFactory.create("unsupported-password-policy-schema-version",{})):t.validatePassword(e)}_getPasswordPolicyInternal(){return null===this.tenantId?this._projectPasswordPolicy:this._tenantPasswordPolicies[this.tenantId]}async _updatePasswordPolicy(){var e=await p(e=this,"GET","/v2/passwordPolicy",o(e,{})),e=new gt(e);null===this.tenantId?this._projectPasswordPolicy=e:this._tenantPasswordPolicies[this.tenantId]=e}_getPersistenceType(){return this.assertedPersistence.persistence.type}_getPersistence(){return this.assertedPersistence.persistence}_updateErrorMap(e){this._errorFactory=new te("auth","Firebase",e())}onAuthStateChanged(e,t,r){return this.registerStateListener(this.authStateSubscription,e,t,r)}beforeAuthStateChanged(e,t){return this.beforeStateQueue.pushCallback(e,t)}onIdTokenChanged(e,t,r){return this.registerStateListener(this.idTokenSubscription,e,t,r)}authStateReady(){return new Promise((t,r)=>{if(this.currentUser)t();else{let e=this.onAuthStateChanged(()=>{e(),t()},r)}})}async revokeAccessToken(e){var t;this.currentUser&&(e={providerId:"apple.com",tokenType:"ACCESS_TOKEN",token:e,idToken:await this.currentUser.getIdToken()},null!=this.tenantId&&(e.tenantId=this.tenantId),await p(t=this,"POST","/v2/accounts:revokeToken",o(t,e)))}toJSON(){return{apiKey:this.config.apiKey,authDomain:this.config.authDomain,appName:this.name,currentUser:this._currentUser?.toJSON()}}async _setRedirectUser(e,t){t=await this.getOrInitRedirectPersistenceManager(t);return null===e?t.removeCurrentUser():t.setCurrentUser(e)}async getOrInitRedirectPersistenceManager(e){return this.redirectPersistenceManager||(m(e=e&&_(e)||this._popupRedirectResolver,this,"argument-error"),this.redirectPersistenceManager=await rt.create(this,[_(e._redirectPersistence)],"redirectUser"),this.redirectUser=await this.redirectPersistenceManager.getCurrentUser()),this.redirectPersistenceManager}async _redirectUserForId(e){return this._isInitialized&&await this.queue(async()=>{}),this._currentUser?._redirectEventId===e?this._currentUser:this.redirectUser?._redirectEventId===e?this.redirectUser:null}async _persistUserIfCurrent(e){if(e===this.currentUser)return this.queue(async()=>this.directlySetCurrentUser(e))}_notifyListenersIfCurrent(e){e===this.currentUser&&this.notifyAuthListeners()}_key(){return`${this.config.authDomain}:${this.config.apiKey}:`+this.name}_startProactiveRefresh(){this.isProactiveRefreshEnabled=!0,this.currentUser&&this._currentUser._startProactiveRefresh()}_stopProactiveRefresh(){this.isProactiveRefreshEnabled=!1,this.currentUser&&this._currentUser._stopProactiveRefresh()}get _currentUser(){return this.currentUser}notifyAuthListeners(){var e;this._isInitialized&&(this.idTokenSubscription.next(this.currentUser),e=this.currentUser?.uid??null,this.lastNotifiedUid!==e)&&(this.lastNotifiedUid=e,this.authStateSubscription.next(this.currentUser))}registerStateListener(t,r,i,n){if(this._deleted)return()=>{};let e="function"==typeof r?r:r.next.bind(r),s=!1;var a=this._isInitialized?Promise.resolve():this._initializationPromise;if(m(a,this,"internal-error"),a.then(()=>{s||e(this.currentUser)}),"function"==typeof r){let e=t.addObserver(r,i,n);return()=>{s=!0,e()}}{let e=t.addObserver(r);return()=>{s=!0,e()}}}async directlySetCurrentUser(e){this.currentUser&&this.currentUser!==e&&this._currentUser._stopProactiveRefresh(),e&&this.isProactiveRefreshEnabled&&e._startProactiveRefresh(),(this.currentUser=e)?await this.assertedPersistence.setCurrentUser(e):await this.assertedPersistence.removeCurrentUser()}queue(e){return this.operations=this.operations.then(e,e),this.operations}get assertedPersistence(){return m(this.persistenceManager,this,"internal-error"),this.persistenceManager}_logFramework(e){e&&!this.frameworks.includes(e)&&(this.frameworks.push(e),this.frameworks.sort(),this.clientVersion=pt(this.config.clientPlatform,this._getFrameworks()))}_getFrameworks(){return this.frameworks}async _getAdditionalHeaders(){var e={"X-Client-Version":this.clientVersion},t=(this.app.options.appId&&(e["X-Firebase-gmpid"]=this.app.options.appId),await this.heartbeatServiceProvider.getImmediate({optional:!0})?.getHeartbeatsHeader()),t=(t&&(e["X-Firebase-Client"]=t),await this._getAppCheckToken());return t&&(e["X-Firebase-AppCheck"]=t),e}async _getAppCheckToken(){var e,t,r;return Bn._isFirebaseServerApp(this.app)&&this.app.settings.appCheckToken?this.app.settings.appCheckToken:((e=await this.appCheckServiceProvider.getImmediate({optional:!0})?.getToken())?.error&&(t="Error while retrieving App Check token: "+e.error,r=[],we.logLevel<=i.WARN)&&we.warn(`Auth (${Bn.SDK_VERSION}): `+t,...r),e?.token)}}function I(e){return s(e)}class vt{constructor(e){var t;this.auth=e,this.observer=null,this.addObserver=(e=e=>this.observer=e,(e=new ce(e,t)).subscribe.bind(e))}get next(){return m(this.observer,this.auth,"internal-error"),this.observer.next.bind(this.observer)}}let _t={async loadJS(){throw new Error("Unable to load external scripts")},recaptchaV2Script:"",recaptchaEnterpriseScript:"",gapiScript:""};function yt(e){return _t.loadJS(e)}function It(e){return"__"+e+Math.floor(1e6*Math.random())}class wt{constructor(e){this.auth=e,this.counter=1e12,this._widgets=new Map}render(e,t){var r=this.counter;return this._widgets.set(r,new bt(e,this.auth.name,t||{})),this.counter++,r}reset(e){e=e||1e12;this._widgets.get(e)?.delete(),this._widgets.delete(e)}getResponse(e){return this._widgets.get(e||1e12)?.getResponse()||""}async execute(e){return this._widgets.get(e||1e12)?.execute(),""}}class Tt{constructor(){this.enterprise=new Et}ready(e){e()}execute(e,t){return Promise.resolve("token")}render(e,t){return""}}class Et{ready(e){e()}execute(e,t){return Promise.resolve("token")}render(e,t){return""}}class bt{constructor(e,t,r){this.params=r,this.timerId=null,this.deleted=!1,this.responseToken=null,this.clickHandler=()=>{this.execute()};r="string"==typeof e?document.getElementById(e):e;m(r,"argument-error",{appName:t}),this.container=r,this.isVisible="invisible"!==this.params.size,this.isVisible?this.execute():this.container.addEventListener("click",this.clickHandler)}getResponse(){return this.checkIfDeleted(),this.responseToken}delete(){this.checkIfDeleted(),this.deleted=!0,this.timerId&&(clearTimeout(this.timerId),this.timerId=null),this.container.removeEventListener("click",this.clickHandler)}execute(){this.checkIfDeleted(),this.timerId||(this.timerId=window.setTimeout(()=>{this.responseToken=(t=>{var r=[],i="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let e=0;e<t;e++)r.push(i.charAt(Math.floor(Math.random()*i.length)));return r.join("")})(50);let{callback:e,"expired-callback":t}=this.params;if(e)try{e(this.responseToken)}catch(e){}this.timerId=window.setTimeout(()=>{if(this.timerId=null,this.responseToken=null,t)try{t()}catch(e){}this.isVisible&&this.execute()},6e4)},500))}checkIfDeleted(){if(this.deleted)throw new Error("reCAPTCHA mock was already deleted!")}}let kt="NO_RECAPTCHA";class St{constructor(e){this.type="recaptcha-enterprise",this.auth=I(e)}async verify(n="verify",e=!1){function s(e,t,r){let i=window.grecaptcha;He(i)?i.enterprise.ready(()=>{i.enterprise.execute(e,{action:n}).then(e=>{t(e)}).catch(()=>{t(kt)})}):r(Error("No reCAPTCHA enterprise script loaded."))}return this.auth.settings.appVerificationDisabledForTesting?(new Tt).execute("siteKey",{action:"verify"}):new Promise((r,i)=>{(async i=>{if(!e){if(null==i.tenantId&&null!=i._agentRecaptchaConfig)return i._agentRecaptchaConfig.siteKey;if(null!=i.tenantId&&void 0!==i._tenantRecaptchaConfigs[i.tenantId])return i._tenantRecaptchaConfigs[i.tenantId].siteKey}return new Promise(async(t,r)=>{je(i,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}).then(e=>{if(void 0!==e.recaptchaKey)return e=new We(e),null==i.tenantId?i._agentRecaptchaConfig=e:i._tenantRecaptchaConfigs[i.tenantId]=e,t(e.siteKey);r(new Error("recaptcha Enterprise site key undefined"))}).catch(e=>{r(e)})})})(this.auth).then(t=>{if(!e&&He(window.grecaptcha))s(t,r,i);else if("undefined"==typeof window)i(new Error("RecaptchaVerifier is only supported in browser"));else{let e=_t.recaptchaEnterpriseScript;0!==e.length&&(e+=t),yt(e).then(()=>{s(t,r,i)}).catch(e=>{i(e)})}}).catch(e=>{i(e)})})}}async function Rt(t,e,r,i=!1,n=!1){t=new St(t);let s;if(n)s=kt;else try{s=await t.verify(r)}catch(e){s=await t.verify(r,!0)}n={...e};return"mfaSmsEnrollment"===r||"mfaSmsSignIn"===r?"phoneEnrollmentInfo"in n?(t=n.phoneEnrollmentInfo.phoneNumber,e=n.phoneEnrollmentInfo.recaptchaToken,Object.assign(n,{phoneEnrollmentInfo:{phoneNumber:t,recaptchaToken:e,captchaResponse:s,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})):"phoneSignInInfo"in n&&(r=n.phoneSignInInfo.recaptchaToken,Object.assign(n,{phoneSignInInfo:{recaptchaToken:r,captchaResponse:s,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})):(i?Object.assign(n,{captchaResp:s}):Object.assign(n,{captchaResponse:s}),Object.assign(n,{clientType:"CLIENT_TYPE_WEB"}),Object.assign(n,{recaptchaVersion:"RECAPTCHA_ENTERPRISE"})),n}async function w(r,i,n,s,e){var t;return"EMAIL_PASSWORD_PROVIDER"===e?r._getRecaptchaConfig()?.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")?(t=await Rt(r,i,n,"getOobCode"===n),s(r,t)):s(r,i).catch(async e=>{var t;return"auth/missing-recaptcha-token"===e.code?(console.log(n+" is protected by reCAPTCHA Enterprise for this project. Automatically triggering the reCAPTCHA flow and restarting the flow."),t=await Rt(r,i,n,"getOobCode"===n),s(r,t)):Promise.reject(e)}):"PHONE_PROVIDER"===e?r._getRecaptchaConfig()?.isProviderEnabled("PHONE_PROVIDER")?(t=await Rt(r,i,n),s(r,t).catch(async e=>{var t;if("AUDIT"===r._getRecaptchaConfig()?.getProviderEnforcementState("PHONE_PROVIDER")&&("auth/missing-recaptcha-token"===e.code||"auth/invalid-app-credential"===e.code))return console.log(`Failed to verify with reCAPTCHA Enterprise. Automatically triggering the reCAPTCHA v2 flow to complete the ${n} flow.`),t=await Rt(r,i,n,!1,!0),s(r,t);return Promise.reject(e)})):(t=await Rt(r,i,n,!1,!0),s(r,t)):Promise.reject(e+" provider is not supported.")}function At(e,t,r){var e=I(e),r=(m(/^https?:\/\//.test(t),e,"invalid-emulator-scheme"),!!r?.disableWarnings),i=Pt(t),{host:t,port:n}=(e=>{var t=Pt(e);return(e=/(\/\/)?([^?#/]+)/.exec(e.substr(t.length)))?(t=e[2].split("@").pop()||"",(e=/^(\[[^\]]+\])(:|$)/.exec(t))?{host:e=e[1],port:Ct(t.substr(e.length+1))}:([e,t]=t.split(":"),{host:e,port:Ct(t)})):{host:"",port:null}})(t),s=null===n?"":":"+n,a={url:i+`//${t}${s}/`},n=Object.freeze({host:t,port:n,protocol:i.replace(":",""),options:Object.freeze({disableWarnings:r})});function o(){var e=document.createElement("p"),t=e.style;e.innerText="Running in emulator mode. Do not use with production credentials.",t.position="fixed",t.width="100%",t.backgroundColor="#ffffff",t.border=".1em solid #000000",t.color="#b50000",t.bottom="0px",t.left="0px",t.margin="0px",t.zIndex="10000",t.textAlign="center",e.classList.add("firebase-emulator-warning"),document.body.appendChild(e)}e._canInitEmulator?(e.config.emulator=a,e.emulatorConfig=n,e.settings.appVerificationDisabledForTesting=!0,G(t)?((async e=>(await fetch(e,{credentials:"include"})).ok)(i+"//"+t+s),Y("Auth",!0)):r||("undefined"!=typeof console&&"function"==typeof console.info&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials."),"undefined"!=typeof window&&"undefined"!=typeof document&&("loading"===document.readyState?window.addEventListener("DOMContentLoaded",o):o()))):(m(e.config.emulator&&e.emulatorConfig,e,"emulator-config-failed"),m(ie(a,e.config.emulator)&&ie(n,e.emulatorConfig),e,"emulator-config-failed"))}function Pt(e){var t=e.indexOf(":");return t<0?"":e.substr(0,t+1)}function Ct(e){return!e||(e=Number(e),isNaN(e))?null:e}class Nt{constructor(e,t){this.providerId=e,this.signInMethod=t}toJSON(){return n("not implemented")}_getIdTokenResponse(e){return n("not implemented")}_linkToIdToken(e,t){return n("not implemented")}_getReauthenticationResolver(e){return n("not implemented")}}async function Ot(e,t){return p(e,"POST","/v1/accounts:resetPassword",o(e,t))}async function Lt(e,t){return p(e,"POST","/v1/accounts:signUp",t)}async function Dt(e,t){return r(e,"POST","/v1/accounts:signInWithPassword",o(e,t))}async function Mt(e,t){return p(e,"POST","/v1/accounts:sendOobCode",o(e,t))}async function Ut(e,t){return Mt(e,t)}async function Ft(e,t){return Mt(e,t)}class Vt extends Nt{constructor(e,t,r,i=null){super("password",r),this._email=e,this._password=t,this._tenantId=i}static _fromEmailAndPassword(e,t){return new Vt(e,t,"password")}static _fromEmailAndCode(e,t,r=null){return new Vt(e,t,"emailLink",r)}toJSON(){return{email:this._email,password:this._password,signInMethod:this.signInMethod,tenantId:this._tenantId}}static fromJSON(e){e="string"==typeof e?JSON.parse(e):e;if(e?.email&&e?.password){if("password"===e.signInMethod)return this._fromEmailAndPassword(e.email,e.password);if("emailLink"===e.signInMethod)return this._fromEmailAndCode(e.email,e.password,e.tenantId)}return null}async _getIdTokenResponse(e){switch(this.signInMethod){case"password":return w(e,{returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signInWithPassword",Dt,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return(async(e,t)=>r(e,"POST","/v1/accounts:signInWithEmailLink",o(e,t)))(e,{email:this._email,oobCode:this._password});default:d(e,"internal-error")}}async _linkToIdToken(e,t){switch(this.signInMethod){case"password":return w(e,{idToken:t,returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",Lt,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return(async(e,t)=>r(e,"POST","/v1/accounts:signInWithEmailLink",o(e,t)))(e,{idToken:t,email:this._email,oobCode:this._password});default:d(e,"internal-error")}}_getReauthenticationResolver(e){return this._getIdTokenResponse(e)}}async function T(e,t){return r(e,"POST","/v1/accounts:signInWithIdp",o(e,t))}class E extends Nt{constructor(){super(...arguments),this.pendingToken=null}static _fromParams(e){var t=new E(e.providerId,e.signInMethod);return e.idToken||e.accessToken?(e.idToken&&(t.idToken=e.idToken),e.accessToken&&(t.accessToken=e.accessToken),e.nonce&&!e.pendingToken&&(t.nonce=e.nonce),e.pendingToken&&(t.pendingToken=e.pendingToken)):e.oauthToken&&e.oauthTokenSecret?(t.accessToken=e.oauthToken,t.secret=e.oauthTokenSecret):d("argument-error"),t}toJSON(){return{idToken:this.idToken,accessToken:this.accessToken,secret:this.secret,nonce:this.nonce,pendingToken:this.pendingToken,providerId:this.providerId,signInMethod:this.signInMethod}}static fromJSON(e){let{providerId:t,signInMethod:r,...i}="string"==typeof e?JSON.parse(e):e;return t&&r?((e=new E(t,r)).idToken=i.idToken||void 0,e.accessToken=i.accessToken||void 0,e.secret=i.secret,e.nonce=i.nonce,e.pendingToken=i.pendingToken||null,e):null}_getIdTokenResponse(e){return T(e,this.buildRequest())}_linkToIdToken(e,t){var r=this.buildRequest();return r.idToken=t,T(e,r)}_getReauthenticationResolver(e){var t=this.buildRequest();return t.autoCreate=!1,T(e,t)}buildRequest(){var e,t={requestUri:"http://localhost",returnSecureToken:!0};return this.pendingToken?t.pendingToken=this.pendingToken:(e={},this.idToken&&(e.id_token=this.idToken),this.accessToken&&(e.access_token=this.accessToken),this.secret&&(e.oauth_token_secret=this.secret),e.providerId=this.providerId,this.nonce&&!this.pendingToken&&(e.nonce=this.nonce),t.postBody=se(e)),t}}async function xt(e,t){return p(e,"POST","/v1/accounts:sendVerificationCode",o(e,t))}let Ht={USER_NOT_FOUND:"user-not-found"};class Wt extends Nt{constructor(e){super("phone","phone"),this.params=e}static _fromVerification(e,t){return new Wt({verificationId:e,verificationCode:t})}static _fromTokenResponse(e,t){return new Wt({phoneNumber:e,temporaryProof:t})}_getIdTokenResponse(e){return(async(e,t)=>r(e,"POST","/v1/accounts:signInWithPhoneNumber",o(e,t)))(e,this._makeVerificationRequest())}_linkToIdToken(e,t){return(async(e,t)=>{if((t=await r(e,"POST","/v1/accounts:signInWithPhoneNumber",o(e,t))).temporaryProof)throw Ve(e,"account-exists-with-different-credential",t);return t})(e,{idToken:t,...this._makeVerificationRequest()})}_getReauthenticationResolver(e){return(async(e,t)=>r(e,"POST","/v1/accounts:signInWithPhoneNumber",o(e,{...t,operation:"REAUTH"}),Ht))(e,this._makeVerificationRequest())}_makeVerificationRequest(){var{temporaryProof:e,phoneNumber:t,verificationId:r,verificationCode:i}=this.params;return e&&t?{temporaryProof:e,phoneNumber:t}:{sessionInfo:r,code:i}}toJSON(){var e={providerId:this.providerId};return this.params.phoneNumber&&(e.phoneNumber=this.params.phoneNumber),this.params.temporaryProof&&(e.temporaryProof=this.params.temporaryProof),this.params.verificationCode&&(e.verificationCode=this.params.verificationCode),this.params.verificationId&&(e.verificationId=this.params.verificationId),e}static fromJSON(e){var{verificationId:e,verificationCode:t,phoneNumber:r,temporaryProof:i}=e="string"==typeof e?JSON.parse(e):e;return t||e||r||i?new Wt({verificationId:e,verificationCode:t,phoneNumber:r,temporaryProof:i}):null}}class jt{constructor(e){var e=ae(oe(e)),t=e.apiKey??null,r=e.oobCode??null,i=(e=>{switch(e){case"recoverEmail":return"RECOVER_EMAIL";case"resetPassword":return"PASSWORD_RESET";case"signIn":return"EMAIL_SIGNIN";case"verifyEmail":return"VERIFY_EMAIL";case"verifyAndChangeEmail":return"VERIFY_AND_CHANGE_EMAIL";case"revertSecondFactorAddition":return"REVERT_SECOND_FACTOR_ADDITION";default:return null}})(e.mode??null);m(t&&r&&i,"argument-error"),this.apiKey=t,this.operation=i,this.code=r,this.continueUrl=e.continueUrl??null,this.languageCode=e.lang??null,this.tenantId=e.tenantId??null}static parseLink(e){t=ae(oe(e=e)).link,r=t?ae(oe(t)).deep_link_id:null;var t,r,i=((i=ae(oe(e)).deep_link_id)?ae(oe(i)).link:null)||i||r||t||e;try{return new jt(i)}catch{return null}}}class qt{constructor(){this.providerId=qt.PROVIDER_ID}static credential(e,t){return Vt._fromEmailAndPassword(e,t)}static credentialWithLink(e,t){t=jt.parseLink(t);return m(t,"argument-error"),Vt._fromEmailAndCode(e,t.code,t.tenantId)}}qt.PROVIDER_ID="password",qt.EMAIL_PASSWORD_SIGN_IN_METHOD="password",qt.EMAIL_LINK_SIGN_IN_METHOD="emailLink";class b{constructor(e){this.providerId=e,this.defaultLanguageCode=null,this.customParameters={}}setDefaultLanguage(e){this.defaultLanguageCode=e}setCustomParameters(e){return this.customParameters=e,this}getCustomParameters(){return this.customParameters}}class Bt extends b{constructor(){super(...arguments),this.scopes=[]}addScope(e){return this.scopes.includes(e)||this.scopes.push(e),this}getScopes(){return[...this.scopes]}}class zt extends Bt{static credentialFromJSON(e){e="string"==typeof e?JSON.parse(e):e;return m("providerId"in e&&"signInMethod"in e,"argument-error"),E._fromParams(e)}credential(e){return this._credential({...e,nonce:e.rawNonce})}_credential(e){return m(e.idToken||e.accessToken,"argument-error"),E._fromParams({...e,providerId:this.providerId,signInMethod:this.providerId})}static credentialFromResult(e){return zt.oauthCredentialFromTaggedObject(e)}static credentialFromError(e){return zt.oauthCredentialFromTaggedObject(e.customData||{})}static oauthCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:e,oauthAccessToken:t,oauthTokenSecret:r,pendingToken:i,nonce:n,providerId:s}=e;if(!(t||r||e||i))return null;if(!s)return null;try{return new zt(s)._credential({idToken:e,accessToken:t,nonce:n,pendingToken:i})}catch(e){return null}}}class k extends Bt{constructor(){super("facebook.com")}static credential(e){return E._fromParams({providerId:k.PROVIDER_ID,signInMethod:k.FACEBOOK_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return k.credentialFromTaggedObject(e)}static credentialFromError(e){return k.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return k.credential(e.oauthAccessToken)}catch{return null}}}k.FACEBOOK_SIGN_IN_METHOD="facebook.com",k.PROVIDER_ID="facebook.com";class S extends Bt{constructor(){super("google.com"),this.addScope("profile")}static credential(e,t){return E._fromParams({providerId:S.PROVIDER_ID,signInMethod:S.GOOGLE_SIGN_IN_METHOD,idToken:e,accessToken:t})}static credentialFromResult(e){return S.credentialFromTaggedObject(e)}static credentialFromError(e){return S.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:e,oauthAccessToken:t}=e;if(!e&&!t)return null;try{return S.credential(e,t)}catch{return null}}}S.GOOGLE_SIGN_IN_METHOD="google.com",S.PROVIDER_ID="google.com";class R extends Bt{constructor(){super("github.com")}static credential(e){return E._fromParams({providerId:R.PROVIDER_ID,signInMethod:R.GITHUB_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return R.credentialFromTaggedObject(e)}static credentialFromError(e){return R.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return R.credential(e.oauthAccessToken)}catch{return null}}}R.GITHUB_SIGN_IN_METHOD="github.com",R.PROVIDER_ID="github.com";class Gt extends Nt{constructor(e,t){super(e,e),this.pendingToken=t}_getIdTokenResponse(e){return T(e,this.buildRequest())}_linkToIdToken(e,t){var r=this.buildRequest();return r.idToken=t,T(e,r)}_getReauthenticationResolver(e){var t=this.buildRequest();return t.autoCreate=!1,T(e,t)}toJSON(){return{signInMethod:this.signInMethod,providerId:this.providerId,pendingToken:this.pendingToken}}static fromJSON(e){var{providerId:e,signInMethod:t,pendingToken:r}="string"==typeof e?JSON.parse(e):e;return e&&t&&r&&e===t?new Gt(e,r):null}static _create(e,t){return new Gt(e,t)}buildRequest(){return{requestUri:"http://localhost",returnSecureToken:!0,pendingToken:this.pendingToken}}}class Kt extends b{constructor(e){m(e.startsWith("saml."),"argument-error"),super(e)}static credentialFromResult(e){return Kt.samlCredentialFromTaggedObject(e)}static credentialFromError(e){return Kt.samlCredentialFromTaggedObject(e.customData||{})}static credentialFromJSON(e){e=Gt.fromJSON(e);return m(e,"argument-error"),e}static samlCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{pendingToken:e,providerId:t}=e;if(!e||!t)return null;try{return Gt._create(t,e)}catch(e){return null}}}class A extends Bt{constructor(){super("twitter.com")}static credential(e,t){return E._fromParams({providerId:A.PROVIDER_ID,signInMethod:A.TWITTER_SIGN_IN_METHOD,oauthToken:e,oauthTokenSecret:t})}static credentialFromResult(e){return A.credentialFromTaggedObject(e)}static credentialFromError(e){return A.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthAccessToken:e,oauthTokenSecret:t}=e;if(!e||!t)return null;try{return A.credential(e,t)}catch{return null}}}async function Jt(e,t){return r(e,"POST","/v1/accounts:signUp",o(e,t))}A.TWITTER_SIGN_IN_METHOD="twitter.com",A.PROVIDER_ID="twitter.com";class P{constructor(e){this.user=e.user,this.providerId=e.providerId,this._tokenResponse=e._tokenResponse,this.operationType=e.operationType}static async _fromIdTokenResponse(e,t,r,i=!1){e=await v._fromIdTokenResponse(e,r,i),i=Yt(r);return new P({user:e,providerId:i,_tokenResponse:r,operationType:t})}static async _forOperation(e,t,r){await e._updateTokensIfNecessary(r,!0);var i=Yt(r);return new P({user:e,providerId:i,_tokenResponse:r,operationType:t})}}function Yt(e){return e.providerId||("phoneNumber"in e?"phone":null)}class $t extends l{constructor(e,t,r,i){super(t.code,t.message),this.operationType=r,this.user=i,Object.setPrototypeOf(this,$t.prototype),this.customData={appName:e.name,tenantId:e.tenantId??void 0,_serverResponse:t.customData._serverResponse,operationType:r}}static _fromErrorAndOperation(e,t,r,i){return new $t(e,t,r,i)}}function Xt(t,r,e,i){return("reauthenticate"===r?e._getReauthenticationResolver(t):e._getIdTokenResponse(t)).catch(e=>{if("auth/multi-factor-auth-required"===e.code)throw $t._fromErrorAndOperation(t,e,r,i);throw e})}function Zt(e){return new Set(e.map(({providerId:e})=>e).filter(e=>!!e))}async function Qt(e,t){var e=s(e),r=(await tr(!0,e,t),r=e.auth,t={idToken:await e.getIdToken(),deleteProvider:[t]},await p(r,"POST","/v1/accounts:update",t)).providerUserInfo;let i=Zt(r||[]);return e.providerData=e.providerData.filter(e=>i.has(e.providerId)),i.has("phone")||(e.phoneNumber=null),await e.auth._persistUserIfCurrent(e),e}async function er(e,t,r=!1){t=await g(e,t._linkToIdToken(e.auth,await e.getIdToken()),r);return P._forOperation(e,"link",t)}async function tr(e,t,r){await $e(t);var i=!1===e?"provider-already-linked":"no-such-provider";m(Zt(t.providerData).has(r)===e,t.auth,i)}async function rr(e,t,r=!1){var i=e.auth;if(Bn._isFirebaseServerApp(i.app))return Promise.reject(u(i));var n="reauthenticate";try{var s=await g(e,Xt(i,n,t,e),r),a=(m(s.idToken,i,"internal-error"),Ge(s.idToken)),o=(m(a,i,"internal-error"),a).sub;return m(e.uid===o,i,"user-mismatch"),P._forOperation(e,n,s)}catch(e){throw"auth/user-not-found"===e?.code&&d(i,"user-mismatch"),e}}async function ir(e,t,r=!1){return Bn._isFirebaseServerApp(e.app)?Promise.reject(u(e)):(t=await Xt(e,"signIn",t),t=await P._fromIdTokenResponse(e,"signIn",t),r||await e._updateCurrentUser(t.user),t)}async function nr(e,t){return ir(I(e),t)}async function sr(e,t){e=s(e);return await tr(!1,e,t.providerId),er(e,t)}async function ar(e,t){return rr(s(e),t)}async function or(e,t){return Bn._isFirebaseServerApp(e.app)?Promise.reject(u(e)):(t=await r(e=I(e),"POST","/v1/accounts:signInWithCustomToken",o(e,{token:t,returnSecureToken:!0})),t=await P._fromIdTokenResponse(e,"signIn",t),await e._updateCurrentUser(t.user),t)}class cr{constructor(e,t){this.factorId=e,this.uid=t.mfaEnrollmentId,this.enrollmentTime=new Date(t.enrolledAt).toUTCString(),this.displayName=t.displayName}static _fromServerResponse(e,t){return"phoneInfo"in t?lr._fromServerResponse(e,t):"totpInfo"in t?dr._fromServerResponse(e,t):d(e,"internal-error")}}class lr extends cr{constructor(e){super("phone",e),this.phoneNumber=e.phoneInfo}static _fromServerResponse(e,t){return new lr(t)}}class dr extends cr{constructor(e){super("totp",e)}static _fromServerResponse(e,t){return new dr(t)}}function hr(e,t,r){m(0<r.url?.length,e,"invalid-continue-uri"),m(void 0===r.dynamicLinkDomain||0<r.dynamicLinkDomain.length,e,"invalid-dynamic-link-domain"),m(void 0===r.linkDomain||0<r.linkDomain.length,e,"invalid-hosting-link-domain"),t.continueUrl=r.url,t.dynamicLinkDomain=r.dynamicLinkDomain,t.linkDomain=r.linkDomain,t.canHandleCodeInApp=r.handleCodeInApp,r.iOS&&(m(0<r.iOS.bundleId.length,e,"missing-ios-bundle-id"),t.iOSBundleId=r.iOS.bundleId),r.android&&(m(0<r.android.packageName.length,e,"missing-android-pkg-name"),t.androidInstallApp=r.android.installApp,t.androidMinimumVersionCode=r.android.minimumVersion,t.androidPackageName=r.android.packageName)}async function ur(e){e=I(e);e._getPasswordPolicyInternal()&&await e._updatePasswordPolicy()}async function pr(e,t){await p(e=s(e),"POST","/v1/accounts:update",o(e,{oobCode:t}))}async function mr(e,t){var r=s(e),i=await Ot(r,{oobCode:t}),e=i.requestType;switch(m(e,r,"internal-error"),e){case"EMAIL_SIGNIN":break;case"VERIFY_AND_CHANGE_EMAIL":m(i.newEmail,r,"internal-error");break;case"REVERT_SECOND_FACTOR_ADDITION":m(i.mfaInfo,r,"internal-error");default:m(i.email,r,"internal-error")}let n=null;return i.mfaInfo&&(n=cr._fromServerResponse(I(r),i.mfaInfo)),{data:{email:("VERIFY_AND_CHANGE_EMAIL"===i.requestType?i.newEmail:i.email)||null,previousEmail:("VERIFY_AND_CHANGE_EMAIL"===i.requestType?i.email:i.newEmail)||null,multiFactorInfo:n},operation:e}}async function gr(e,t){var r=Re()?Se():"http://localhost",e=(await p(e=s(e),"POST","/v1/accounts:createAuthUri",o(e,{identifier:t,continueUri:r}))).signinMethods;return e||[]}async function fr(e,t){var r=s(e),i={requestType:"VERIFY_EMAIL",idToken:await e.getIdToken()},t=(t&&hr(r.auth,i,t),await Mt(r.auth,i)).email;t!==e.email&&await e.reload()}async function vr(e,t,r){var i=s(e),t={requestType:"VERIFY_AND_CHANGE_EMAIL",idToken:await e.getIdToken(),newEmail:t},r=(r&&hr(i.auth,t,r),await Mt(i.auth,t)).email;r!==e.email&&await e.reload()}async function _r(e,{displayName:t,photoURL:r}){var i;void 0===t&&void 0===r||(i=await(e=s(e)).getIdToken(),i=await g(e,(async(e,t)=>p(e,"POST","/v1/accounts:update",t))(e.auth,{idToken:i,displayName:t,photoUrl:r,returnSecureToken:!0})),e.displayName=i.displayName||null,e.photoURL=i.photoUrl||null,(t=e.providerData.find(({providerId:e})=>"password"===e))&&(t.displayName=e.displayName,t.photoURL=e.photoURL),await e._updateTokensIfNecessary(i))}async function yr(e,t,r){var i=e.auth,n={idToken:await e.getIdToken(),returnSecureToken:!0},t=(t&&(n.email=t),r&&(n.password=r),await g(e,(async(e,t)=>p(e,"POST","/v1/accounts:update",t))(i,n)));await e._updateTokensIfNecessary(t,!0)}class Ir{constructor(e,t,r={}){this.isNewUser=e,this.providerId=t,this.profile=r}}class wr extends Ir{constructor(e,t,r,i){super(e,t,r),this.username=i}}class Tr extends Ir{constructor(e,t){super(e,"facebook.com",t)}}class Er extends wr{constructor(e,t){super(e,"github.com",t,"string"==typeof t?.login?t?.login:null)}}class br extends Ir{constructor(e,t){super(e,"google.com",t)}}class kr extends wr{constructor(e,t,r){super(e,"twitter.com",t,r)}}function Sr(e){var{user:e,_tokenResponse:t}=e;if(e.isAnonymous&&!t)return{providerId:null,isNewUser:!1,profile:null};var r=t;if(!r)return null;var i=r.providerId,n=r.rawUserInfo?JSON.parse(r.rawUserInfo):{},s=r.isNewUser||"identitytoolkit#SignupNewUserResponse"===r.kind;if(!i&&r?.idToken){e=Ge(r.idToken)?.firebase?.sign_in_provider;if(e)return e="anonymous"!==e&&"custom"!==e?e:null,new Ir(s,e)}if(!i)return null;switch(i){case"facebook.com":return new Tr(s,n);case"github.com":return new Er(s,n);case"google.com":return new br(s,n);case"twitter.com":return new kr(s,n,r.screenName||null);case"custom":case"anonymous":return new Ir(s,null);default:return new Ir(s,i,n)}}class Rr{constructor(e,t,r){this.type=e,this.credential=t,this.user=r}static _fromIdtoken(e,t){return new Rr("enroll",e,t)}static _fromMfaPendingCredential(e){return new Rr("signin",e)}toJSON(){return{multiFactorSession:{["enroll"===this.type?"idToken":"pendingCredential"]:this.credential}}}static fromJSON(e){if(e?.multiFactorSession){if(e.multiFactorSession?.pendingCredential)return Rr._fromMfaPendingCredential(e.multiFactorSession.pendingCredential);if(e.multiFactorSession?.idToken)return Rr._fromIdtoken(e.multiFactorSession.idToken)}return null}}class Ar{constructor(e,t,r){this.session=e,this.hints=t,this.signInResolver=r}static _fromError(e,i){let n=I(e),s=i.customData._serverResponse;e=(s.mfaInfo||[]).map(e=>cr._fromServerResponse(n,e));m(s.mfaPendingCredential,n,"internal-error");let a=Rr._fromMfaPendingCredential(s.mfaPendingCredential);return new Ar(a,e,async e=>{var e=await e._process(n,a),t=(delete s.mfaInfo,delete s.mfaPendingCredential,{...s,idToken:e.idToken,refreshToken:e.refreshToken});switch(i.operationType){case"signIn":var r=await P._fromIdTokenResponse(n,i.operationType,t);return await n._updateCurrentUser(r.user),r;case"reauthenticate":return m(i.user,n,"internal-error"),P._forOperation(i.user,i.operationType,t);default:d(n,"internal-error")}})}async resolveSignIn(e){return this.signInResolver(e)}}function Pr(e,t){return p(e,"POST","/v2/accounts/mfaEnrollment:start",o(e,t))}class Cr{constructor(t){this.user=t,this.enrolledFactors=[],t._onReload(e=>{e.mfaInfo&&(this.enrolledFactors=e.mfaInfo.map(e=>cr._fromServerResponse(t.auth,e)))})}static _fromUser(e){return new Cr(e)}async getSession(){return Rr._fromIdtoken(await this.user.getIdToken(),this.user)}async enroll(e,t){var r=await this.getSession(),e=await g(this.user,e._process(this.user.auth,r,t));return await this.user._updateTokensIfNecessary(e),this.user.reload()}async unenroll(e){let t="string"==typeof e?e:e.uid;var r,i,e=await this.user.getIdToken();try{var n=await g(this.user,(r=this.user.auth,i={idToken:e,mfaEnrollmentId:t},p(r,"POST","/v2/accounts/mfaEnrollment:withdraw",o(r,i))));this.enrolledFactors=this.enrolledFactors.filter(({uid:e})=>e!==t),await this.user._updateTokensIfNecessary(n),await this.user.reload()}catch(e){throw e}}}let Nr=new WeakMap;let Or="__sak";class Lr{constructor(e,t){this.storageRetriever=e,this.type=t}_isAvailable(){try{return this.storage?(this.storage.setItem(Or,"1"),this.storage.removeItem(Or),Promise.resolve(!0)):Promise.resolve(!1)}catch{return Promise.resolve(!1)}}_set(e,t){return this.storage.setItem(e,JSON.stringify(t)),Promise.resolve()}_get(e){e=this.storage.getItem(e);return Promise.resolve(e?JSON.parse(e):null)}_remove(e){return this.storage.removeItem(e),Promise.resolve()}get storage(){return this.storageRetriever()}}class Dr extends Lr{constructor(){super(()=>window.localStorage,"LOCAL"),this.boundEventHandler=(e,t)=>this.onStorageEvent(e,t),this.listeners={},this.localCache={},this.pollTimer=null,this.fallbackToPolling=ut(),this._shouldAllowMigration=!0}forAllChangedKeys(e){for(var t of Object.keys(this.listeners)){var r=this.storage.getItem(t),i=this.localCache[t];r!==i&&e(t,i,r)}}onStorageEvent(e,r=!1){if(e.key){let t=e.key;r?this.detachListener():this.stopPolling();var i=()=>{var e=this.storage.getItem(t);!r&&this.localCache[t]===e||this.notifyListeners(t,e)},n=this.storage.getItem(t);Q()&&10===document.documentMode&&n!==e.newValue&&e.newValue!==e.oldValue?setTimeout(i,10):i()}else this.forAllChangedKeys((e,t,r)=>{this.notifyListeners(e,r)})}notifyListeners(e,t){this.localCache[e]=t;e=this.listeners[e];if(e)for(var r of Array.from(e))r(t&&JSON.parse(t))}startPolling(){this.stopPolling(),this.pollTimer=setInterval(()=>{this.forAllChangedKeys((e,t,r)=>{this.onStorageEvent(new StorageEvent("storage",{key:e,oldValue:t,newValue:r}),!0)})},1e3)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}attachListener(){window.addEventListener("storage",this.boundEventHandler)}detachListener(){window.removeEventListener("storage",this.boundEventHandler)}_addListener(e,t){0===Object.keys(this.listeners).length&&(this.fallbackToPolling?this.startPolling():this.attachListener()),this.listeners[e]||(this.listeners[e]=new Set,this.localCache[e]=this.storage.getItem(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size)&&delete this.listeners[e],0===Object.keys(this.listeners).length&&(this.detachListener(),this.stopPolling())}async _set(e,t){await super._set(e,t),this.localCache[e]=JSON.stringify(t)}async _get(e){var t=await super._get(e);return this.localCache[e]=JSON.stringify(t),t}async _remove(e){await super._remove(e),delete this.localCache[e]}}Dr.type="LOCAL";let Mr=Dr;class Ur extends Lr{constructor(){super(()=>window.sessionStorage,"SESSION")}_addListener(e,t){}_removeListener(e,t){}}Ur.type="SESSION";let Fr=Ur;class Vr{constructor(e){this.eventTarget=e,this.handlersMap={},this.boundEventHandler=this.handleEvent.bind(this)}static _getInstance(t){var e=this.receivers.find(e=>e.isListeningto(t));return e||(e=new Vr(t),this.receivers.push(e),e)}isListeningto(e){return this.eventTarget===e}async handleEvent(e){let t=e,{eventId:r,eventType:i,data:n}=t.data;var e=this.handlersMap[i];e?.size&&(t.ports[0].postMessage({status:"ack",eventId:r,eventType:i}),e=Array.from(e).map(async e=>e(t.origin,n)),e=await Promise.all(e.map(async e=>{try{return{fulfilled:!0,value:await e}}catch(e){return{fulfilled:!1,reason:e}}})),t.ports[0].postMessage({status:"done",eventId:r,eventType:i,response:e}))}_subscribe(e,t){0===Object.keys(this.handlersMap).length&&this.eventTarget.addEventListener("message",this.boundEventHandler),this.handlersMap[e]||(this.handlersMap[e]=new Set),this.handlersMap[e].add(t)}_unsubscribe(e,t){this.handlersMap[e]&&t&&this.handlersMap[e].delete(t),t&&0!==this.handlersMap[e].size||delete this.handlersMap[e],0===Object.keys(this.handlersMap).length&&this.eventTarget.removeEventListener("message",this.boundEventHandler)}}function xr(e="",t=10){let r="";for(let e=0;e<t;e++)r+=Math.floor(10*Math.random());return e+r}Vr.receivers=[];class Hr{constructor(e){this.target=e,this.handlers=new Set}removeMessageHandler(e){e.messageChannel&&(e.messageChannel.port1.removeEventListener("message",e.onMessage),e.messageChannel.port1.close()),this.handlers.delete(e)}async _send(e,t,a=50){let o="undefined"!=typeof MessageChannel?new MessageChannel:null;if(!o)throw new Error("connection_unavailable");let c,l;return new Promise((r,i)=>{let n=xr("",20),s=(o.port1.start(),setTimeout(()=>{i(new Error("unsupported_event"))},a));l={messageChannel:o,onMessage(e){var t=e;if(t.data.eventId===n)switch(t.data.status){case"ack":clearTimeout(s),c=setTimeout(()=>{i(new Error("timeout"))},3e3);break;case"done":clearTimeout(c),r(t.data.response);break;default:clearTimeout(s),clearTimeout(c),i(new Error("invalid_response"))}}},this.handlers.add(l),o.port1.addEventListener("message",l.onMessage),this.target.postMessage({eventType:e,eventId:n,data:t},[o.port2])}).finally(()=>{l&&this.removeMessageHandler(l)})}}function C(){return window}function Wr(){return void 0!==C().WorkerGlobalScope&&"function"==typeof C().importScripts}let jr="firebaseLocalStorageDb",qr="firebaseLocalStorage",Br="fbase_key";class zr{constructor(e){this.request=e}toPromise(){return new Promise((e,t)=>{this.request.addEventListener("success",()=>{e(this.request.result)}),this.request.addEventListener("error",()=>{t(this.request.error)})})}}function Gr(e,t){return e.transaction([qr],t?"readwrite":"readonly").objectStore(qr)}function Kr(){let i=indexedDB.open(jr,1);return new Promise((t,r)=>{i.addEventListener("error",()=>{r(i.error)}),i.addEventListener("upgradeneeded",()=>{var e=i.result;try{e.createObjectStore(qr,{keyPath:Br})}catch(e){r(e)}}),i.addEventListener("success",async()=>{var e=i.result;e.objectStoreNames.contains(qr)?t(e):(e.close(),e=indexedDB.deleteDatabase(jr),await new zr(e).toPromise(),t(await Kr()))})})}async function Jr(e,t,r){e=Gr(e,!0).put({fbase_key:t,value:r});return new zr(e).toPromise()}function Yr(e,t){e=Gr(e,!0).delete(t);return new zr(e).toPromise()}class $r{constructor(){this.type="LOCAL",this._shouldAllowMigration=!0,this.listeners={},this.localCache={},this.pollTimer=null,this.pendingWrites=0,this.receiver=null,this.sender=null,this.serviceWorkerReceiverAvailable=!1,this.activeServiceWorker=null,this._workerInitializationPromise=this.initializeServiceWorkerMessaging().then(()=>{},()=>{})}async _openDb(){return this.db||(this.db=await Kr()),this.db}async _withRetries(e){let t=0;for(;;)try{return await e(await this._openDb())}catch(e){if(3<t++)throw e;this.db&&(this.db.close(),this.db=void 0)}}async initializeServiceWorkerMessaging(){return Wr()?this.initializeReceiver():this.initializeSender()}async initializeReceiver(){this.receiver=Vr._getInstance(Wr()?self:null),this.receiver._subscribe("keyChanged",async(e,t)=>({keyProcessed:(await this._poll()).includes(t.key)})),this.receiver._subscribe("ping",async(e,t)=>["keyChanged"])}async initializeSender(){var e;this.activeServiceWorker=await(async()=>{if(!navigator?.serviceWorker)return null;try{return(await navigator.serviceWorker.ready).active}catch{return null}})(),this.activeServiceWorker&&(this.sender=new Hr(this.activeServiceWorker),e=await this.sender._send("ping",{},800))&&e[0]?.fulfilled&&e[0]?.value.includes("keyChanged")&&(this.serviceWorkerReceiverAvailable=!0)}async notifyServiceWorker(e){if(this.sender&&this.activeServiceWorker&&(navigator?.serviceWorker?.controller||null)===this.activeServiceWorker)try{await this.sender._send("keyChanged",{key:e},this.serviceWorkerReceiverAvailable?800:50)}catch{}}async _isAvailable(){try{var e;return indexedDB?(await Jr(e=await Kr(),Or,"1"),await Yr(e,Or),!0):!1}catch{}return!1}async _withPendingWrite(e){this.pendingWrites++;try{await e()}finally{this.pendingWrites--}}async _set(t,r){return this._withPendingWrite(async()=>(await this._withRetries(e=>Jr(e,t,r)),this.localCache[t]=r,this.notifyServiceWorker(t)))}async _get(t){var e=await this._withRetries(e=>(async(e,t)=>(e=Gr(e,!1).get(t),void 0===(t=await new zr(e).toPromise())?null:t.value))(e,t));return this.localCache[t]=e}async _remove(t){return this._withPendingWrite(async()=>(await this._withRetries(e=>Yr(e,t)),delete this.localCache[t],this.notifyServiceWorker(t)))}async _poll(){var e=await this._withRetries(e=>{e=Gr(e,!1).getAll();return new zr(e).toPromise()});if(!e)return[];if(0!==this.pendingWrites)return[];var t,r=[],i=new Set;if(0!==e.length)for(var{fbase_key:n,value:s}of e)i.add(n),JSON.stringify(this.localCache[n])!==JSON.stringify(s)&&(this.notifyListeners(n,s),r.push(n));for(t of Object.keys(this.localCache))this.localCache[t]&&!i.has(t)&&(this.notifyListeners(t,null),r.push(t));return r}notifyListeners(e,t){this.localCache[e]=t;e=this.listeners[e];if(e)for(var r of Array.from(e))r(t)}startPolling(){this.stopPolling(),this.pollTimer=setInterval(async()=>this._poll(),800)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}_addListener(e,t){0===Object.keys(this.listeners).length&&this.startPolling(),this.listeners[e]||(this.listeners[e]=new Set,this._get(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size)&&delete this.listeners[e],0===Object.keys(this.listeners).length&&this.stopPolling()}}$r.type="LOCAL";let Xr=$r;function Zr(e,t){return p(e,"POST","/v2/accounts/mfaSignIn:start",o(e,t))}let Qr=It("rcb"),ei=new Pe(3e4,6e4);class ti{constructor(){this.hostLanguage="",this.counter=0,this.librarySeparatelyLoaded=!!C().grecaptcha?.render}load(n,s=""){var e;return m((e=s).length<=6&&/^\s*[a-zA-Z0-9\-]*\s*$/.test(e),n,"argument-error"),this.shouldResolveImmediately(s)&&xe(C().grecaptcha)?Promise.resolve(C().grecaptcha):new Promise((t,r)=>{let i=C().setTimeout(()=>{r(h(n,"network-request-failed"))},ei.get());C()[Qr]=()=>{C().clearTimeout(i),delete C()[Qr];var e=C().grecaptcha;if(e&&xe(e)){let r=e.render;e.render=(e,t)=>{e=r(e,t);return this.counter++,e},this.hostLanguage=s,t(e)}else r(h(n,"internal-error"))},yt(_t.recaptchaV2Script+"?"+se({onload:Qr,render:"explicit",hl:s})).catch(()=>{clearTimeout(i),r(h(n,"internal-error"))})})}clearedOneInstance(){this.counter--}shouldResolveImmediately(e){return!!C().grecaptcha?.render&&(e===this.hostLanguage||0<this.counter||this.librarySeparatelyLoaded)}}class ri{async load(e){return new wt(e)}clearedOneInstance(){}}let ii="recaptcha",ni={theme:"light",type:"image"};class si{constructor(e,t,r={...ni}){this.parameters=r,this.type=ii,this.destroyed=!1,this.widgetId=null,this.tokenChangeListeners=new Set,this.renderPromise=null,this.recaptcha=null,this.auth=I(e),this.isInvisible="invisible"===this.parameters.size,m("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment");r="string"==typeof t?document.getElementById(t):t;m(r,this.auth,"argument-error"),this.container=r,this.parameters.callback=this.makeTokenCallback(this.parameters.callback),this._recaptchaLoader=new(this.auth.settings.appVerificationDisabledForTesting?ri:ti),this.validateStartingState()}async verify(){this.assertNotDestroyed();let e=await this.render(),i=this.getAssertedRecaptcha();var t=i.getResponse(e);return t||new Promise(t=>{let r=e=>{e&&(this.tokenChangeListeners.delete(r),t(e))};this.tokenChangeListeners.add(r),this.isInvisible&&i.execute(e)})}render(){try{this.assertNotDestroyed()}catch(e){return Promise.reject(e)}return this.renderPromise||(this.renderPromise=this.makeRenderPromise().catch(e=>{throw this.renderPromise=null,e})),this.renderPromise}_reset(){this.assertNotDestroyed(),null!==this.widgetId&&this.getAssertedRecaptcha().reset(this.widgetId)}clear(){this.assertNotDestroyed(),this.destroyed=!0,this._recaptchaLoader.clearedOneInstance(),this.isInvisible||this.container.childNodes.forEach(e=>{this.container.removeChild(e)})}validateStartingState(){m(!this.parameters.sitekey,this.auth,"argument-error"),m(this.isInvisible||!this.container.hasChildNodes(),this.auth,"argument-error"),m("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment")}makeTokenCallback(r){return t=>{var e;this.tokenChangeListeners.forEach(e=>e(t)),"function"==typeof r?r(t):"string"==typeof r&&"function"==typeof(e=C()[r])&&e(t)}}assertNotDestroyed(){m(!this.destroyed,this.auth,"internal-error")}async makeRenderPromise(){if(await this.init(),!this.widgetId){let e=this.container;var t;this.isInvisible||(t=document.createElement("div"),e.appendChild(t),e=t),this.widgetId=this.getAssertedRecaptcha().render(e,this.parameters)}return this.widgetId}async init(){m(Re()&&!Wr(),this.auth,"internal-error"),await(()=>{let t=null;return new Promise(e=>{"complete"===document.readyState?e():(t=()=>e(),window.addEventListener("load",t))}).catch(e=>{throw t&&window.removeEventListener("load",t),e})})(),this.recaptcha=await this._recaptchaLoader.load(this.auth,this.auth.languageCode||void 0);var e=await((await p(this.auth,"GET","/v1/recaptchaParams")).recaptchaSiteKey||"");m(e,this.auth,"internal-error"),this.parameters.sitekey=e}getAssertedRecaptcha(){return m(this.recaptcha,this.auth,"internal-error"),this.recaptcha}}class ai{constructor(e,t){this.verificationId=e,this.onConfirmation=t}confirm(e){e=Wt._fromVerification(this.verificationId,e);return this.onConfirmation(e)}}async function oi(t,r,i){if(!t._getRecaptchaConfig())try{n=await je(e=I(e=t),{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}),n=new We(n),null==e.tenantId?e._agentRecaptchaConfig=n:e._tenantRecaptchaConfigs[e.tenantId]=n,await(!n.isAnyProviderEnabled()||!new St(e).verify())}catch(e){console.log("Failed to initialize reCAPTCHA Enterprise config. Triggering the reCAPTCHA v2 verification.")}var e,n,s,a,o,c,l;try{let e;return("session"in(e="string"==typeof r?{phoneNumber:r}:r)?(s=e.session,"phoneNumber"in e?(m("enroll"===s.type,t,"internal-error"),a={idToken:s.credential,phoneEnrollmentInfo:{phoneNumber:e.phoneNumber,clientType:"CLIENT_TYPE_WEB"}},(await w(t,a,"mfaSmsEnrollment",async(e,t)=>t.phoneEnrollmentInfo.captchaResponse===kt?(m(i?.type===ii,e,"argument-error"),Pr(e,await ci(e,t,i))):Pr(e,t),"PHONE_PROVIDER").catch(e=>Promise.reject(e))).phoneSessionInfo):(m("signin"===s.type,t,"internal-error"),m(o=e.multiFactorHint?.uid||e.multiFactorUid,t,"missing-multi-factor-info"),c={mfaPendingCredential:s.credential,mfaEnrollmentId:o,phoneSignInInfo:{clientType:"CLIENT_TYPE_WEB"}},(await w(t,c,"mfaSmsSignIn",async(e,t)=>t.phoneSignInInfo.captchaResponse===kt?(m(i?.type===ii,e,"argument-error"),Zr(e,await ci(e,t,i))):Zr(e,t),"PHONE_PROVIDER").catch(e=>Promise.reject(e))).phoneResponseInfo)):(l={phoneNumber:e.phoneNumber,clientType:"CLIENT_TYPE_WEB"},await w(t,l,"sendVerificationCode",async(e,t)=>t.captchaResponse===kt?(m(i?.type===ii,e,"argument-error"),xt(e,await ci(e,t,i))):xt(e,t),"PHONE_PROVIDER").catch(e=>Promise.reject(e)))).sessionInfo}finally{i?._reset()}}async function ci(e,t,r){m(r.type===ii,e,"argument-error");var i,n,s,r=await r.verify(),e=(m("string"==typeof r,e,"argument-error"),{...t});return"phoneEnrollmentInfo"in e?(t=e.phoneEnrollmentInfo.phoneNumber,n=e.phoneEnrollmentInfo.captchaResponse,s=e.phoneEnrollmentInfo.clientType,i=e.phoneEnrollmentInfo.recaptchaVersion,Object.assign(e,{phoneEnrollmentInfo:{phoneNumber:t,recaptchaToken:r,captchaResponse:n,clientType:s,recaptchaVersion:i}})):"phoneSignInInfo"in e?(t=e.phoneSignInInfo.captchaResponse,n=e.phoneSignInInfo.clientType,s=e.phoneSignInInfo.recaptchaVersion,Object.assign(e,{phoneSignInInfo:{recaptchaToken:r,captchaResponse:t,clientType:n,recaptchaVersion:s}})):Object.assign(e,{recaptchaToken:r}),e}class N{constructor(e){this.providerId=N.PROVIDER_ID,this.auth=I(e)}verifyPhoneNumber(e,t){return oi(this.auth,e,s(t))}static credential(e,t){return Wt._fromVerification(e,t)}static credentialFromResult(e){return N.credentialFromTaggedObject(e)}static credentialFromError(e){return N.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){var t;return e&&({phoneNumber:e,temporaryProof:t}=e,e)&&t?Wt._fromTokenResponse(e,t):null}}function li(e,t){return t?_(t):(m(e._popupRedirectResolver,e,"argument-error"),e._popupRedirectResolver)}N.PROVIDER_ID="phone",N.PHONE_SIGN_IN_METHOD="phone";class di extends Nt{constructor(e){super("custom","custom"),this.params=e}_getIdTokenResponse(e){return T(e,this._buildIdpRequest())}_linkToIdToken(e,t){return T(e,this._buildIdpRequest(t))}_getReauthenticationResolver(e){return T(e,this._buildIdpRequest())}_buildIdpRequest(e){var t={requestUri:this.params.requestUri,sessionId:this.params.sessionId,postBody:this.params.postBody,tenantId:this.params.tenantId,pendingToken:this.params.pendingToken,returnSecureToken:!0,returnIdpCredential:!0};return e&&(t.idToken=e),t}}function hi(e){return ir(e.auth,new di(e),e.bypassAuthState)}function ui(e){var{auth:t,user:r}=e;return m(r,t,"internal-error"),rr(r,new di(e),e.bypassAuthState)}async function pi(e){var{auth:t,user:r}=e;return m(r,t,"internal-error"),er(r,new di(e),e.bypassAuthState)}class mi{constructor(e,t,r,i,n=!1){this.auth=e,this.resolver=r,this.user=i,this.bypassAuthState=n,this.pendingPromise=null,this.eventManager=null,this.filter=Array.isArray(t)?t:[t]}execute(){return new Promise(async(e,t)=>{this.pendingPromise={resolve:e,reject:t};try{this.eventManager=await this.resolver._initialize(this.auth),await this.onExecution(),this.eventManager.registerConsumer(this)}catch(e){this.reject(e)}})}async onAuthEvent(e){var{urlResponse:e,sessionId:t,postBody:r,tenantId:i,error:n,type:s}=e;if(n)this.reject(n);else{n={auth:this.auth,requestUri:e,sessionId:t,tenantId:i||void 0,postBody:r||void 0,user:this.user,bypassAuthState:this.bypassAuthState};try{this.resolve(await this.getIdpTask(s)(n))}catch(e){this.reject(e)}}}onError(e){this.reject(e)}getIdpTask(e){switch(e){case"signInViaPopup":case"signInViaRedirect":return hi;case"linkViaPopup":case"linkViaRedirect":return pi;case"reauthViaPopup":case"reauthViaRedirect":return ui;default:d(this.auth,"internal-error")}}resolve(e){a(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.resolve(e),this.unregisterAndCleanUp()}reject(e){a(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.reject(e),this.unregisterAndCleanUp()}unregisterAndCleanUp(){this.eventManager&&this.eventManager.unregisterConsumer(this),this.pendingPromise=null,this.cleanUp()}}let gi=new Pe(2e3,1e4);class O extends mi{constructor(e,t,r,i,n){super(e,t,i,n),this.provider=r,this.authWindow=null,this.pollId=null,O.currentPopupAction&&O.currentPopupAction.cancel(),O.currentPopupAction=this}async executeNotNull(){var e=await this.execute();return m(e,this.auth,"internal-error"),e}async onExecution(){a(1===this.filter.length,"Popup operations only handle one event");var e=xr();this.authWindow=await this.resolver._openPopup(this.auth,this.provider,this.filter[0],e),this.authWindow.associatedEvent=e,this.resolver._originValidation(this.auth).catch(e=>{this.reject(e)}),this.resolver._isIframeWebStorageSupported(this.auth,e=>{e||this.reject(h(this.auth,"web-storage-unsupported"))}),this.pollUserCancellation()}get eventId(){return this.authWindow?.associatedEvent||null}cancel(){this.reject(h(this.auth,"cancelled-popup-request"))}cleanUp(){this.authWindow&&this.authWindow.close(),this.pollId&&window.clearTimeout(this.pollId),this.authWindow=null,this.pollId=null,O.currentPopupAction=null}pollUserCancellation(){let e=()=>{this.authWindow?.window?.closed?this.pollId=window.setTimeout(()=>{this.pollId=null,this.reject(h(this.auth,"popup-closed-by-user"))},8e3):this.pollId=window.setTimeout(e,gi.get())};e()}}O.currentPopupAction=null;let fi="pendingRedirect",vi=new Map;class _i extends mi{constructor(e,t,r=!1){super(e,["signInViaRedirect","linkViaRedirect","reauthViaRedirect","unknown"],t,void 0,r),this.eventId=null}async execute(){let t=vi.get(this.auth._key());if(!t){try{let e=await(async(e,t)=>{var r;return t=Ti(t),!!await(e=wi(e))._isAvailable()&&(r="true"===await e._get(t),await e._remove(t),r)})(this.resolver,this.auth)?await super.execute():null;t=()=>Promise.resolve(e)}catch(e){t=()=>Promise.reject(e)}vi.set(this.auth._key(),t)}return this.bypassAuthState||vi.set(this.auth._key(),()=>Promise.resolve(null)),t()}async onAuthEvent(e){if("signInViaRedirect"===e.type)return super.onAuthEvent(e);if("unknown"===e.type)this.resolve(null);else if(e.eventId){var t=await this.auth._redirectUserForId(e.eventId);if(t)return this.user=t,super.onAuthEvent(e);this.resolve(null)}}async onExecution(){}cleanUp(){}}async function yi(e,t){return wi(e)._set(Ti(t),"true")}function Ii(e,t){vi.set(e._key(),t)}function wi(e){return _(e._redirectPersistence)}function Ti(e){return y(fi,e.config.apiKey,e.name)}function Ei(e,t,r){return(async(e,t,r)=>{var i;return Bn._isFirebaseServerApp(e.app)?Promise.reject(u(e)):(i=I(e),be(e,t,b),await i._initializationPromise,await yi(e=li(i,r),i),e._openRedirect(i,t,"signInViaRedirect"))})(e,t,r)}function bi(e,t,r){return(async(e,t,r)=>{if(be((e=s(e)).auth,t,b),Bn._isFirebaseServerApp(e.auth.app))return Promise.reject(u(e.auth));await e.auth._initializationPromise;await yi(r=li(e.auth,r),e.auth);var i=await Ri(e);return r._openRedirect(e.auth,t,"reauthViaRedirect",i)})(e,t,r)}function ki(e,t,r){return(async(e,t,r)=>{be((e=s(e)).auth,t,b),await e.auth._initializationPromise;var r=li(e.auth,r),i=(await tr(!1,e,t.providerId),await yi(r,e.auth),await Ri(e));return r._openRedirect(e.auth,t,"linkViaRedirect",i)})(e,t,r)}async function Si(e,t,r=!1){var i;return Bn._isFirebaseServerApp(e.app)?Promise.reject(u(e)):(i=li(e=I(e),t),(i=await new _i(e,i,r).execute())&&!r&&(delete i.user._redirectEventId,await e._persistUserIfCurrent(i.user),await e._setRedirectUser(null,t)),i)}async function Ri(e){var t=xr(e.uid+":::");return e._redirectEventId=t,await e.auth._setRedirectUser(e),await e.auth._persistUserIfCurrent(e),t}class Ai{constructor(e){this.auth=e,this.cachedEventUids=new Set,this.consumers=new Set,this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1,this.lastProcessedEventTime=Date.now()}registerConsumer(e){this.consumers.add(e),this.queuedRedirectEvent&&this.isEventForConsumer(this.queuedRedirectEvent,e)&&(this.sendToConsumer(this.queuedRedirectEvent,e),this.saveEventToCache(this.queuedRedirectEvent),this.queuedRedirectEvent=null)}unregisterConsumer(e){this.consumers.delete(e)}onEvent(t){if(this.hasEventBeenHandled(t))return!1;let r=!1;return this.consumers.forEach(e=>{this.isEventForConsumer(t,e)&&(r=!0,this.sendToConsumer(t,e),this.saveEventToCache(t))}),this.hasHandledPotentialRedirect||!(e=>{switch(e.type){case"signInViaRedirect":case"linkViaRedirect":case"reauthViaRedirect":return 1;case"unknown":return Ci(e);default:return}})(t)||(this.hasHandledPotentialRedirect=!0,r)||(this.queuedRedirectEvent=t,r=!0),r}sendToConsumer(e,t){var r;e.error&&!Ci(e)?(r=e.error.code?.split("auth/")[1]||"internal-error",t.onError(h(this.auth,r))):t.onAuthEvent(e)}isEventForConsumer(e,t){var r=null===t.eventId||!!e.eventId&&e.eventId===t.eventId;return t.filter.includes(e.type)&&r}hasEventBeenHandled(e){return 6e5<=Date.now()-this.lastProcessedEventTime&&this.cachedEventUids.clear(),this.cachedEventUids.has(Pi(e))}saveEventToCache(e){this.cachedEventUids.add(Pi(e)),this.lastProcessedEventTime=Date.now()}}function Pi(e){return[e.type,e.eventId,e.sessionId,e.tenantId].filter(e=>e).join("-")}function Ci({type:e,error:t}){return"unknown"===e&&"auth/no-auth-event"===t?.code}async function Ni(e,t={}){return p(e,"GET","/v1/projects",t)}let Oi=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,Li=/^https?/;async function Di(e){if(!e.config.emulator){var t,r=(await Ni(e)).authorizedDomains;for(t of r)try{if((e=>{var t,r=Se(),{protocol:i,hostname:n}=new URL(r);return e.startsWith("chrome-extension://")?""===(t=new URL(e)).hostname&&""===n?"chrome-extension:"===i&&e.replace("chrome-extension://","")===r.replace("chrome-extension://",""):"chrome-extension:"===i&&t.hostname===n:Li.test(i)&&(Oi.test(e)?n===e:(r=e.replace(/\./g,"\\."),(t=new RegExp("^(.+\\."+r+"|"+r+")$","i")).test(n)))})(t))return}catch{}d(e,"unauthorized-domain")}}let Mi=new Pe(3e4,6e4);function Ui(){var t=C().___jsl;if(t?.H)for(var e of Object.keys(t.H))if(t.H[e].r=t.H[e].r||[],t.H[e].L=t.H[e].L||[],t.H[e].r=[...t.H[e].L],t.CP)for(let e=0;e<t.CP.length;e++)t.CP[e]=null}function Fi(n){return new Promise((e,t)=>{function r(){Ui(),gapi.load("gapi.iframes",{callback:()=>{e(gapi.iframes.getContext())},ontimeout:()=>{Ui(),t(h(n,"network-request-failed"))},timeout:Mi.get()})}if(C().gapi?.iframes?.Iframe)e(gapi.iframes.getContext());else{var i;if(!C().gapi?.load)return i=It("iframefcb"),C()[i]=()=>{gapi.load?r():t(h(n,"network-request-failed"))},yt(_t.gapiScript+"?onload="+i).catch(e=>t(e));r()}}).catch(e=>{throw Vi=null,e})}let Vi=null;let xi=new Pe(5e3,15e3),Hi="__/auth/iframe",Wi="emulator/auth/iframe",ji={style:{position:"absolute",top:"-100px",width:"1px",height:"1px"},"aria-hidden":"true",tabindex:"-1"},qi=new Map([["identitytoolkit.googleapis.com","p"],["staging-identitytoolkit.sandbox.googleapis.com","s"],["test-identitytoolkit.sandbox.googleapis.com","t"]]);async function Bi(a){i=a;var e,t,r,i=await(Vi=Vi||Fi(i)),n=C().gapi;return m(n,a,"internal-error"),i.open({where:document.body,url:(m((t=(i=a).config).authDomain,i,"auth-domain-config-required"),e=t.emulator?Ce(t,Wi):`https://${i.config.authDomain}/`+Hi,t={apiKey:t.apiKey,appName:i.name,v:Bn.SDK_VERSION},(r=qi.get(i.config.apiHost))&&(t.eid=r),(r=i._getFrameworks()).length&&(t.fw=r.join(",")),e+"?"+se(t).slice(1)),messageHandlersFilter:n.iframes.CROSS_ORIGIN_IFRAMES_FILTER,attributes:ji,dontclear:!0},s=>new Promise(async(e,t)=>{await s.restyle({setHideOnLeave:!1});let r=h(a,"network-request-failed"),i=C().setTimeout(()=>{t(r)},xi.get());function n(){C().clearTimeout(i),e(s)}s.ping(n).then(n,()=>{t(r)})}))}let zi={location:"yes",resizable:"yes",statusbar:"yes",toolbar:"no"};class Gi{constructor(e){this.window=e,this.associatedEvent=null}close(){if(this.window)try{this.window.close()}catch(e){}}}function Ki(e,t,r,i=500,n=600){var s=Math.max((window.screen.availHeight-n)/2,0).toString(),a=Math.max((window.screen.availWidth-i)/2,0).toString();let o="";var i={...zi,width:i.toString(),height:n.toString(),top:s,left:a},n=c().toLowerCase(),s=(r&&(o=at(n)?"_blank":r),nt(n)&&(t=t||"http://localhost",i.scrollbars="yes"),Object.entries(i).reduce((e,[t,r])=>""+e+t+`=${r},`,""));if([a=c()]=[n],ht(a)&&window.navigator?.standalone&&"_self"!==o)return r=t||"",i=o,(n=document.createElement("a")).href=r,n.target=i,(r=document.createEvent("MouseEvent")).initMouseEvent("click",!0,!0,window,1,0,0,0,0,!1,!1,!1,!1,1,null),n.dispatchEvent(r),new Gi(null);a=window.open(t||"",o,s);m(a,e,"popup-blocked");try{a.focus()}catch(e){}return new Gi(a)}let Ji="__/auth/handler",Yi="emulator/auth/handler",$i=encodeURIComponent("fac");async function Xi(e,t,r,i,n,s){m(e.config.authDomain,e,"auth-domain-config-required"),m(e.config.apiKey,e,"invalid-api-key");var a={apiKey:e.config.apiKey,appName:e.name,authType:r,redirectUrl:i,v:Bn.SDK_VERSION,eventId:n};if(t instanceof b){t.setDefaultLanguage(e.languageCode),a.providerId=t.providerId||"",(e=>{for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))return;return 1})(t.getCustomParameters())||(a.customParameters=JSON.stringify(t.getCustomParameters()));for(var[o,c]of Object.entries(s||{}))a[o]=c}t instanceof Bt&&0<(r=t.getScopes().filter(e=>""!==e)).length&&(a.scopes=r.join(",")),e.tenantId&&(a.tid=e.tenantId);var l,d=a;for(l of Object.keys(d))void 0===d[l]&&delete d[l];i=await e._getAppCheckToken(),n=i?`#${$i}=`+encodeURIComponent(i):"";return`${s=[e.config][0],s.emulator?Ce(s,Yi):`https://${s.authDomain}/`+Ji}?`+se(d).slice(1)+n}let Zi="webStorageSupport";class Qi{constructor(){this.eventManagers={},this.iframes={},this.originValidationPromises={},this._redirectPersistence=Fr,this._completeRedirectFn=Si,this._overrideRedirectResult=Ii}async _openPopup(e,t,r,i){return a(this.eventManagers[e._key()]?.manager,"_initialize() not called before _openPopup()"),Ki(e,await Xi(e,t,r,Se(),i),xr())}async _openRedirect(e,t,r,i){await this._originValidation(e);e=await Xi(e,t,r,Se(),i);return C().location.href=e,new Promise(()=>{})}_initialize(e){let r=e._key();if(this.eventManagers[r]){let{manager:e,promise:t}=this.eventManagers[r];return e?Promise.resolve(e):(a(t,"If manager is not set, promise should be"),t)}let t=this.initAndGetManager(e);return this.eventManagers[r]={promise:t},t.catch(()=>{delete this.eventManagers[r]}),t}async initAndGetManager(t){var e=await Bi(t);let r=new Ai(t);return e.register("authEvent",e=>(m(e?.authEvent,t,"invalid-auth-event"),{status:r.onEvent(e.authEvent)?"ACK":"ERROR"}),gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER),this.eventManagers[t._key()]={manager:r},this.iframes[t._key()]=e,r}_isIframeWebStorageSupported(t,r){this.iframes[t._key()].send(Zi,{type:Zi},e=>{e=e?.[0]?.[Zi];void 0!==e&&r(!!e),d(t,"internal-error")},gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER)}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=Di(e)),this.originValidationPromises[t]}get _shouldInitProactively(){return ut()||st()||ht()}}let en=Qi;class tn extends class{constructor(e){this.factorId=e}_process(e,t,r){switch(t.type){case"enroll":return this._finalizeEnroll(e,t.credential,r);case"signin":return this._finalizeSignIn(e,t.credential);default:return n("unexpected MultiFactorSessionType")}}}{constructor(e){super("phone"),this.credential=e}static _fromCredential(e){return new tn(e)}_finalizeEnroll(e,t,r){return e=e,t={idToken:t,displayName:r,phoneVerificationInfo:this.credential._makeVerificationRequest()},p(e,"POST","/v2/accounts/mfaEnrollment:finalize",o(e,t))}_finalizeSignIn(e,t){return e=e,t={mfaPendingCredential:t,phoneVerificationInfo:this.credential._makeVerificationRequest()},p(e,"POST","/v2/accounts/mfaSignIn:finalize",o(e,t))}}class rn{constructor(){}static assertion(e){return tn._fromCredential(e)}}rn.FACTOR_ID="phone";var e="@firebase/auth";class nn{constructor(e){this.auth=e,this.internalListeners=new Map}getUid(){return this.assertAuthConfigured(),this.auth.currentUser?.uid||null}async getToken(e){return this.assertAuthConfigured(),await this.auth._initializationPromise,this.auth.currentUser?{accessToken:await this.auth.currentUser.getIdToken(e)}:null}addAuthTokenListener(t){var e;this.assertAuthConfigured(),this.internalListeners.has(t)||(e=this.auth.onIdTokenChanged(e=>{t(e?.stsTokenManager.accessToken||null)}),this.internalListeners.set(t,e),this.updateProactiveRefresh())}removeAuthTokenListener(e){this.assertAuthConfigured();var t=this.internalListeners.get(e);t&&(this.internalListeners.delete(e),t(),this.updateProactiveRefresh())}assertAuthConfigured(){m(this.auth._initializationPromise,"dependent-sdk-initialized-before-auth")}updateProactiveRefresh(){0<this.internalListeners.size?this.auth._startProactiveRefresh():this.auth._stopProactiveRefresh()}}var sn;function an(){return window}z()?._authIdTokenMaxAge,_t={loadJS(i){return new Promise((e,r)=>{var t=document.createElement("script");t.setAttribute("src",i),t.onload=e,t.onerror=e=>{var t=h("internal-error");t.customData=e,r(t)},t.type="text/javascript",t.charset="UTF-8",(document.getElementsByTagName("head")?.[0]??document).appendChild(t)})},gapiScript:"https://apis.google.com/js/api.js",recaptchaV2Script:"https://www.google.com/recaptcha/api.js",recaptchaEnterpriseScript:"https://www.google.com/recaptcha/enterprise.js?render="},sn="Browser",Bn._registerComponent(new me("auth",(e,{options:t})=>{var r=e.getProvider("app").getImmediate(),i=e.getProvider("heartbeat"),e=e.getProvider("app-check-internal"),{apiKey:n,authDomain:s}=r.options,n=(m(n&&!n.includes(":"),"invalid-api-key",{appName:r.name}),{apiKey:n,authDomain:s,clientPlatform:sn,apiHost:"identitytoolkit.googleapis.com",tokenApiHost:"securetoken.googleapis.com",apiScheme:"https",sdkClientVersion:pt(sn)}),s=new ft(r,i,e,n);return r=s,e=(i=t)?.persistence||[],e=(Array.isArray(e)?e:[e]).map(_),i?.errorMap&&r._updateErrorMap(i.errorMap),r._initializeWithPersistence(e,i?.popupRedirectResolver),s},"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((e,t,r)=>{e.getProvider("auth-internal").initialize()})),Bn._registerComponent(new me("auth-internal",e=>{var e=I(e.getProvider("auth").getImmediate());return e=e,new nn(e)},"PRIVATE").setInstantiationMode("EXPLICIT")),Bn.registerVersion(e,"1.11.0",(e=>{switch(e){case"Node":return"node";case"ReactNative":return"rn";case"Worker":return"webworker";case"Cordova":return"cordova";case"WebExtension":return"web-extension";default:return}})(sn)),Bn.registerVersion(e,"1.11.0","esm2020");async function on(e,t,r){var i=an().BuildInfo,n=(a(t.sessionId,"AuthEvent did not contain a session ID"),await(async e=>(e=(t=>{if(a(/[0-9a-zA-Z]+/.test(t),"Can only convert alpha-numeric strings"),"undefined"!=typeof TextEncoder)return(new TextEncoder).encode(t);var e=new ArrayBuffer(t.length),r=new Uint8Array(e);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r})(e),e=await crypto.subtle.digest("SHA-256",e),(e=Array.from(new Uint8Array(e))).map(e=>e.toString(16).padStart(2,"0")).join("")))(t.sessionId)),s={};return ht()?s.ibi=i.packageName:ct()?s.apn=i.packageName:d(e,"operation-not-supported-in-this-environment"),i.displayName&&(s.appDisplayName=i.displayName),s.sessionId=n,Xi(e,r,t.type,void 0,t.eventId??void 0,s)}function cn(i){let n=an().cordova;return new Promise(r=>{n.plugins.browsertab.isAvailable(e=>{let t=null;e?n.plugins.browsertab.openUrl(i):t=n.InAppBrowser.open(i,(e=c(),/(iPad|iPhone|iPod).*OS 7_\d/i.test(e)||/(iPad|iPhone|iPod).*OS 8_\d/i.test(e)?"_blank":"_system"),"location=yes"),r(t)})})}let ln=20;class dn extends Ai{constructor(){super(...arguments),this.passiveListeners=new Set,this.initPromise=new Promise(e=>{this.resolveInitialized=e})}addPassiveListener(e){this.passiveListeners.add(e)}removePassiveListener(e){this.passiveListeners.delete(e)}resetRedirect(){this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1}onEvent(t){return this.resolveInitialized(),this.passiveListeners.forEach(e=>e(t)),super.onEvent(t)}async initialized(){await this.initPromise}}function hn(e,t,r=null){return{type:t,eventId:r,urlResponse:null,sessionId:(()=>{var t=[],r="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let e=0;e<ln;e++){var i=Math.floor(Math.random()*r.length);t.push(r.charAt(i))}return t.join("")})(),postBody:null,tenantId:e.tenantId,error:h(e,"no-auth-event")}}async function un(e){var t=await mn()._get(gn(e));return t&&await mn()._remove(gn(e)),t}function pn(e,t){n=fn(t=t),i=n.link?decodeURIComponent(n.link):void 0,r=fn(i).link,n=n.deep_link_id?decodeURIComponent(n.deep_link_id):void 0;var r,i,n=fn(n).link||n||r||i||t;return n.includes("/__/auth/callback")?(t=(i=((r=fn(n)).firebaseError?(e=>{try{return JSON.parse(e)}catch(e){return null}})(decodeURIComponent(r.firebaseError)):null)?.code?.split("auth/")?.[1])?h(i):null)?{type:e.type,eventId:e.eventId,tenantId:e.tenantId,error:t,urlResponse:null,sessionId:null,postBody:null}:{type:e.type,eventId:e.eventId,tenantId:e.tenantId,sessionId:e.sessionId,urlResponse:n,postBody:null}:null}function mn(){return _(Mr)}function gn(e){return y("authEvent",e.config.apiKey,e.name)}function fn(e){var t;return e?.includes("?")?([t,...e]=e.split("?"),ae(e.join("?"))):{}}class vn{constructor(){this._redirectPersistence=Fr,this._shouldInitProactively=!0,this.eventManagers=new Map,this.originValidationPromises={},this._completeRedirectFn=Si,this._overrideRedirectResult=Ii}async _initialize(e){var t=e._key();let r=this.eventManagers.get(t);return r||(r=new dn(e),this.eventManagers.set(t,r),this.attachCallbackListeners(e,r)),r}_openPopup(e){d(e,"operation-not-supported-in-this-environment")}async _openRedirect(e,t,r,i){s=e,m("function"==typeof(n=an())?.universalLinks?.subscribe,s,"invalid-cordova-configuration",{missingPlugin:"cordova-universal-links-plugin-fix"}),m(void 0!==n?.BuildInfo?.packageName,s,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-buildInfo"}),m("function"==typeof n?.cordova?.plugins?.browsertab?.openUrl,s,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),m("function"==typeof n?.cordova?.plugins?.browsertab?.isAvailable,s,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),m("function"==typeof n?.cordova?.InAppBrowser?.open,s,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-inappbrowser"});var n=await this._initialize(e),s=(await n.initialized(),n.resetRedirect(),vi.clear(),await this._originValidation(e),hn(e,r,i)),r=(r=e,i=s,await mn()._set(gn(r),i),await on(e,s,t));return(async(a,o,c)=>{let l=an().cordova,d=()=>{};try{await new Promise((t,e)=>{let r=null;function i(){t();var e=l.plugins.browsertab?.close;"function"==typeof e&&e(),"function"==typeof c?.close&&c.close()}function n(){r=r||window.setTimeout(()=>{e(h(a,"redirect-cancelled-by-user"))},2e3)}function s(){"visible"===document?.visibilityState&&n()}o.addPassiveListener(i),document.addEventListener("resume",n,!1),ct()&&document.addEventListener("visibilitychange",s,!1),d=()=>{o.removePassiveListener(i),document.removeEventListener("resume",n,!1),document.removeEventListener("visibilitychange",s,!1),r&&window.clearTimeout(r)}})}finally{d()}})(e,n,await cn(r))}_isIframeWebStorageSupported(e,t){throw new Error("Method not implemented.")}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=(async e=>{var t=an().BuildInfo,r={};ht()?r.iosBundleId=t.packageName:ct()?r.androidPackageName=t.packageName:d(e,"operation-not-supported-in-this-environment"),await Ni(e,r)})(e)),this.originValidationPromises[t]}attachCallbackListeners(i,n){var{universalLinks:e,handleOpenURL:t,BuildInfo:r}=an();let s=setTimeout(async()=>{await un(i),n.onEvent(yn())},500),a=async e=>{clearTimeout(s);var t=await un(i);let r=null;t&&e?.url&&(r=pn(t,e.url)),n.onEvent(r||yn())},o=(void 0!==e&&"function"==typeof e.subscribe&&e.subscribe(null,a),t),c=r.packageName.toLowerCase()+"://";an().handleOpenURL=async e=>{if(e.toLowerCase().startsWith(c)&&a({url:e}),"function"==typeof o)try{o(e)}catch(e){console.error(e)}}}}let _n=vn;function yn(){return{type:"unknown",eventId:null,sessionId:null,urlResponse:null,postBody:null,tenantId:null,error:h("no-auth-event")}}function In(){return self?.location?.protocol||null}function wn(e=c()){return!("file:"!==In()&&"ionic:"!==In()&&"capacitor:"!==In()||!e.toLowerCase().match(/iphone|ipad|ipod|android/))}function Tn(e=c()){return Q()&&11===document?.documentMode||([e=c()]=[e],/Edge\/\d+/.test(e))}function En(){try{var e=self.localStorage,t=xr();if(e)return e.setItem(t,"1"),e.removeItem(t),!Tn()||ee()}catch(e){return bn()&&ee()}return!1}function bn(){return"undefined"!=typeof global&&"WorkerGlobalScope"in global&&"importScripts"in global}function kn(){return("http:"===In()||"https:"===In()||X()||wn())&&!(Z()||$())&&En()&&!bn()}function Sn(){return wn()&&"undefined"!=typeof document}let L={LOCAL:"local",NONE:"none",SESSION:"session"},Rn=m,An="persistence";async function Pn(e){await e._initializationPromise;var t=Cn(),r=y(An,e.config.apiKey,e.name);t&&t.setItem(r,e._getPersistenceType())}function Cn(){try{return("undefined"!=typeof window?window:null)?.sessionStorage||null}catch(e){return null}}let Nn=m;class D{constructor(){this.browserResolver=_(en),this.cordovaResolver=_(_n),this.underlyingResolver=null,this._redirectPersistence=Fr,this._completeRedirectFn=Si,this._overrideRedirectResult=Ii}async _initialize(e){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._initialize(e)}async _openPopup(e,t,r,i){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._openPopup(e,t,r,i)}async _openRedirect(e,t,r,i){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._openRedirect(e,t,r,i)}_isIframeWebStorageSupported(e,t){this.assertedUnderlyingResolver._isIframeWebStorageSupported(e,t)}_originValidation(e){return this.assertedUnderlyingResolver._originValidation(e)}get _shouldInitProactively(){return Sn()||this.browserResolver._shouldInitProactively}get assertedUnderlyingResolver(){return Nn(this.underlyingResolver,"internal-error"),this.underlyingResolver}async selectUnderlyingResolver(){var e;this.underlyingResolver||(e=await(!!Sn()&&new Promise(e=>{let t=setTimeout(()=>{e(!1)},1e3);document.addEventListener("deviceready",()=>{clearTimeout(t),e(!0)})})),this.underlyingResolver=e?this.cordovaResolver:this.browserResolver)}}function On(e){return e.unwrap()}function Ln(e,t){var r,i=t.customData?._tokenResponse;"auth/multi-factor-auth-required"===t?.code?t.resolver=new Un(e,(r=t,e=s(e=e),m((r=r).customData.operationType,e,"argument-error"),m(r.customData._serverResponse?.mfaPendingCredential,e,"argument-error"),Ar._fromError(e,r))):i&&(r=Dn(e=t))&&(e.credential=r,e.tenantId=i.tenantId||void 0,e.email=i.email||void 0,e.phoneNumber=i.phoneNumber||void 0)}function Dn(e){var t=(e instanceof l?e.customData:e)._tokenResponse;if(!t)return null;if(!(e instanceof l)&&"temporaryProof"in t&&"phoneNumber"in t)return N.credentialFromResult(e);var r=t.providerId;if(!r||r===ge.PASSWORD)return null;let i;switch(r){case ge.GOOGLE:i=S;break;case ge.FACEBOOK:i=k;break;case ge.GITHUB:i=R;break;case ge.TWITTER:i=A;break;default:var{oauthIdToken:n,oauthAccessToken:s,oauthTokenSecret:a,pendingToken:o,nonce:c}=t;return s||a||n||o?o?r.startsWith("saml.")?Gt._create(r,o):E._fromParams({providerId:r,signInMethod:r,pendingToken:o,idToken:n,accessToken:s}):new zt(r).credential({idToken:n,accessToken:s,rawNonce:c}):null}return e instanceof l?i.credentialFromError(e):i.credentialFromResult(e)}function M(t,e){return e.catch(e=>{throw e instanceof l&&Ln(t,e),e}).then(e=>{var t=e.operationType,r=e.user;return{operationType:t,credential:Dn(e),additionalUserInfo:Sr(e),user:U.getOrCreate(r)}})}async function Mn(t,e){let r=await e;return{verificationId:r.verificationId,confirm:e=>M(t,r.confirm(e))}}class Un{constructor(e,t){this.resolver=t,this.auth=e.wrapped()}get session(){return this.resolver.session}get hints(){return this.resolver.hints}resolveSignIn(e){return M(On(this.auth),this.resolver.resolveSignIn(e))}}class U{constructor(e){this._delegate=e,this.multiFactor=(e=s(e=e),Nr.has(e)||Nr.set(e,Cr._fromUser(e)),Nr.get(e))}static getOrCreate(e){return U.USER_MAP.has(e)||U.USER_MAP.set(e,new U(e)),U.USER_MAP.get(e)}delete(){return this._delegate.delete()}reload(){return this._delegate.reload()}toJSON(){return this._delegate.toJSON()}getIdTokenResult(e){return this._delegate.getIdTokenResult(e)}getIdToken(e){return this._delegate.getIdToken(e)}linkAndRetrieveDataWithCredential(e){return this.linkWithCredential(e)}async linkWithCredential(e){return M(this.auth,sr(this._delegate,e))}async linkWithPhoneNumber(e,t){return Mn(this.auth,(async(e,t,r)=>{let i=s(e);return await tr(!1,i,"phone"),e=await oi(i.auth,t,s(r)),new ai(e,e=>sr(i,e))})(this._delegate,e,t))}async linkWithPopup(e){return M(this.auth,(async(e,t,r)=>(be((e=s(e)).auth,t,b),r=li(e.auth,r),new O(e.auth,"linkViaPopup",t,r,e).executeNotNull()))(this._delegate,e,D))}async linkWithRedirect(e){return await Pn(I(this.auth)),ki(this._delegate,e,D)}reauthenticateAndRetrieveDataWithCredential(e){return this.reauthenticateWithCredential(e)}async reauthenticateWithCredential(e){return M(this.auth,ar(this._delegate,e))}reauthenticateWithPhoneNumber(e,t){return Mn(this.auth,(async(e,t,r)=>{let i=s(e);return Bn._isFirebaseServerApp(i.auth.app)?Promise.reject(u(i.auth)):(e=await oi(i.auth,t,s(r)),new ai(e,e=>ar(i,e)))})(this._delegate,e,t))}reauthenticateWithPopup(e){return M(this.auth,(async(e,t,r)=>(e=s(e),Bn._isFirebaseServerApp(e.auth.app)?Promise.reject(h(e.auth,"operation-not-supported-in-this-environment")):(be(e.auth,t,b),r=li(e.auth,r),new O(e.auth,"reauthViaPopup",t,r,e).executeNotNull())))(this._delegate,e,D))}async reauthenticateWithRedirect(e){return await Pn(I(this.auth)),bi(this._delegate,e,D)}sendEmailVerification(e){return fr(this._delegate,e)}async unlink(e){return await Qt(this._delegate,e),this}updateEmail(e){return t=this._delegate,e=e,t=s(t),Bn._isFirebaseServerApp(t.auth.app)?Promise.reject(u(t.auth)):yr(t,e,null);var t}updatePassword(e){return yr(s(this._delegate),null,e)}updatePhoneNumber(e){return(async(e,t)=>{if(e=s(e),Bn._isFirebaseServerApp(e.auth.app))return Promise.reject(u(e.auth));await er(e,t)})(this._delegate,e)}updateProfile(e){return _r(this._delegate,e)}verifyBeforeUpdateEmail(e,t){return vr(this._delegate,e,t)}get emailVerified(){return this._delegate.emailVerified}get isAnonymous(){return this._delegate.isAnonymous}get metadata(){return this._delegate.metadata}get phoneNumber(){return this._delegate.phoneNumber}get providerData(){return this._delegate.providerData}get refreshToken(){return this._delegate.refreshToken}get tenantId(){return this._delegate.tenantId}get displayName(){return this._delegate.displayName}get email(){return this._delegate.email}get photoURL(){return this._delegate.photoURL}get providerId(){return this._delegate.providerId}get uid(){return this._delegate.uid}get auth(){return this._delegate.auth}}U.USER_MAP=new WeakMap;let Fn=m;class Vn{constructor(e,t){var r,i;this.app=e,t.isInitialized()?this._delegate=t.getImmediate():(r=e.options.apiKey,Fn(r,"invalid-api-key",{appName:e.name}),Fn(r,"invalid-api-key",{appName:e.name}),i="undefined"!=typeof window?D:void 0,this._delegate=t.initialize({options:{persistence:((e,t)=>{var r=((e,t)=>{var r=Cn();if(!r)return[];switch(e=y(An,e,t),r.getItem(e)){case L.NONE:return[tt];case L.LOCAL:return[Xr,Fr];case L.SESSION:return[Fr];default:return[]}})(e,t);if("undefined"==typeof self||r.includes(Xr)||r.push(Xr),"undefined"!=typeof window)for(var i of[Mr,Fr])r.includes(i)||r.push(i);return r.includes(tt)||r.push(tt),r})(r,e.name),popupRedirectResolver:i}}),this._delegate._updateErrorMap(_e)),this.linkUnderlyingAuth()}get emulatorConfig(){return this._delegate.emulatorConfig}get currentUser(){return this._delegate.currentUser?U.getOrCreate(this._delegate.currentUser):null}get languageCode(){return this._delegate.languageCode}set languageCode(e){this._delegate.languageCode=e}get settings(){return this._delegate.settings}get tenantId(){return this._delegate.tenantId}set tenantId(e){this._delegate.tenantId=e}useDeviceLanguage(){this._delegate.useDeviceLanguage()}signOut(){return this._delegate.signOut()}useEmulator(e,t){At(this._delegate,e,t)}applyActionCode(e){return pr(this._delegate,e)}checkActionCode(e){return mr(this._delegate,e)}confirmPasswordReset(e,t){return(async(t,e,r)=>{await Ot(s(t),{oobCode:e,newPassword:r}).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&ur(t),e})})(this._delegate,e,t)}async createUserWithEmailAndPassword(e,t){return M(this._delegate,(async(t,e,r)=>{var i;return Bn._isFirebaseServerApp(t.app)?Promise.reject(u(t)):(e=await w(i=I(t),{returnSecureToken:!0,email:e,password:r,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",Jt,"EMAIL_PASSWORD_PROVIDER").catch(e=>{throw"auth/password-does-not-meet-requirements"===e.code&&ur(t),e}),r=await P._fromIdTokenResponse(i,"signIn",e),await i._updateCurrentUser(r.user),r)})(this._delegate,e,t))}fetchProvidersForEmail(e){return this.fetchSignInMethodsForEmail(e)}fetchSignInMethodsForEmail(e){return gr(this._delegate,e)}isSignInWithEmailLink(e){return this._delegate,e=e,"EMAIL_SIGNIN"===jt.parseLink(e)?.operation}async getRedirectResult(){Fn(kn(),this._delegate,"operation-not-supported-in-this-environment");t=this._delegate,e=D,await I(t)._initializationPromise;var e,t=await Si(t,e,!1);return t?M(this._delegate,Promise.resolve(t)):{credential:null,user:null}}addFrameworkForLogging(e){I(this._delegate)._logFramework(e)}onAuthStateChanged(e,t,r){var{next:e,error:t,complete:r}=xn(e,t,r);return this._delegate.onAuthStateChanged(e,t,r)}onIdTokenChanged(e,t,r){var{next:e,error:t,complete:r}=xn(e,t,r);return this._delegate.onIdTokenChanged(e,t,r)}sendSignInLinkToEmail(e,t){return(async(e,t,r)=>{let i=I(e);t=e={requestType:"EMAIL_SIGNIN",email:t,clientType:"CLIENT_TYPE_WEB"},m((r=r).handleCodeInApp,i,"argument-error"),r&&hr(i,t,r),await w(i,e,"getOobCode",Ft,"EMAIL_PASSWORD_PROVIDER")})(this._delegate,e,t)}sendPasswordResetEmail(e,t){return(async(e,t,r)=>{e=I(e),t={requestType:"PASSWORD_RESET",email:t,clientType:"CLIENT_TYPE_WEB"},r&&hr(e,t,r),await w(e,t,"getOobCode",Ut,"EMAIL_PASSWORD_PROVIDER")})(this._delegate,e,t||void 0)}async setPersistence(e){var t,r;t=this._delegate,r=e,Rn(Object.values(L).includes(r),t,"invalid-persistence-type"),Z()?Rn(r!==L.SESSION,t,"unsupported-persistence-type"):$()?Rn(r===L.NONE,t,"unsupported-persistence-type"):bn()?Rn(r===L.NONE||r===L.LOCAL&&ee(),t,"unsupported-persistence-type"):Rn(r===L.NONE||En(),t,"unsupported-persistence-type");let i;switch(e){case L.SESSION:i=Fr;break;case L.LOCAL:var n=await _(Xr)._isAvailable();i=n?Xr:Mr;break;case L.NONE:i=tt;break;default:return d("argument-error",{appName:this._delegate.name})}return this._delegate.setPersistence(i)}signInAndRetrieveDataWithCredential(e){return this.signInWithCredential(e)}signInAnonymously(){return M(this._delegate,(async e=>{var t;return Bn._isFirebaseServerApp(e.app)?Promise.reject(u(e)):(await(e=I(e))._initializationPromise,e.currentUser?.isAnonymous?new P({user:e.currentUser,providerId:null,operationType:"signIn"}):(t=await Jt(e,{returnSecureToken:!0}),t=await P._fromIdTokenResponse(e,"signIn",t,!0),await e._updateCurrentUser(t.user),t))})(this._delegate))}signInWithCredential(e){return M(this._delegate,nr(this._delegate,e))}signInWithCustomToken(e){return M(this._delegate,or(this._delegate,e))}signInWithEmailAndPassword(e,t){return M(this._delegate,(r=this._delegate,e=e,t=t,Bn._isFirebaseServerApp(r.app)?Promise.reject(u(r)):nr(s(r),qt.credential(e,t)).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&ur(r),e})));var r}signInWithEmailLink(e,t){return M(this._delegate,(async(e,t,r)=>Bn._isFirebaseServerApp(e.app)?Promise.reject(u(e)):(e=s(e),m((t=qt.credentialWithLink(t,r||Se()))._tenantId===(e.tenantId||null),e,"tenant-id-mismatch"),nr(e,t)))(this._delegate,e,t))}signInWithPhoneNumber(e,t){return Mn(this._delegate,(async(e,t,r)=>{if(Bn._isFirebaseServerApp(e.app))return Promise.reject(u(e));let i=I(e);return e=await oi(i,t,s(r)),new ai(e,e=>nr(i,e))})(this._delegate,e,t))}async signInWithPopup(e){return Fn(kn(),this._delegate,"operation-not-supported-in-this-environment"),M(this._delegate,(async(e,t,r)=>{var i;return Bn._isFirebaseServerApp(e.app)?Promise.reject(h(e,"operation-not-supported-in-this-environment")):(i=I(e),be(e,t,b),e=li(i,r),new O(i,"signInViaPopup",t,e).executeNotNull())})(this._delegate,e,D))}async signInWithRedirect(e){return Fn(kn(),this._delegate,"operation-not-supported-in-this-environment"),await Pn(this._delegate),Ei(this._delegate,e,D)}updateCurrentUser(e){return this._delegate.updateCurrentUser(e)}verifyPasswordResetCode(e){return(async(e,t)=>(e=(await mr(s(e),t)).data).email)(this._delegate,e)}unwrap(){return this._delegate}_delete(){return this._delegate._delete()}linkUnderlyingAuth(){this._delegate.wrapped=()=>this}}function xn(e,t,r){let i=e,n=("function"!=typeof e&&({next:i,error:t,complete:r}=e),i);return{next:e=>n(e&&U.getOrCreate(e)),error:t,complete:r}}Vn.Persistence=L;class Hn{static credential(e,t){return N.credential(e,t)}constructor(){this.providerId="phone",this._delegate=new N(On(F.default.auth()))}verifyPhoneNumber(e,t){return this._delegate.verifyPhoneNumber(e,t)}unwrap(){return this._delegate}}Hn.PHONE_SIGN_IN_METHOD=N.PHONE_SIGN_IN_METHOD,Hn.PROVIDER_ID=N.PROVIDER_ID;let Wn=m;class jn{constructor(e,t,r=F.default.app()){Wn(r.options?.apiKey,"invalid-api-key",{appName:r.name}),this._delegate=new si(r.auth(),e,t),this.type=this._delegate.type}clear(){this._delegate.clear()}render(){return this._delegate.render()}verify(){return this._delegate.verify()}}(e=F.default).INTERNAL.registerComponent(new me("auth-compat",e=>{var t=e.getProvider("app-compat").getImmediate(),e=e.getProvider("auth");return new Vn(t,e)},"PUBLIC").setServiceProps({ActionCodeInfo:{Operation:{EMAIL_SIGNIN:fe.EMAIL_SIGNIN,PASSWORD_RESET:fe.PASSWORD_RESET,RECOVER_EMAIL:fe.RECOVER_EMAIL,REVERT_SECOND_FACTOR_ADDITION:fe.REVERT_SECOND_FACTOR_ADDITION,VERIFY_AND_CHANGE_EMAIL:fe.VERIFY_AND_CHANGE_EMAIL,VERIFY_EMAIL:fe.VERIFY_EMAIL}},EmailAuthProvider:qt,FacebookAuthProvider:k,GithubAuthProvider:R,GoogleAuthProvider:S,OAuthProvider:zt,SAMLAuthProvider:Kt,PhoneAuthProvider:Hn,PhoneMultiFactorGenerator:rn,RecaptchaVerifier:jn,TwitterAuthProvider:A,Auth:Vn,AuthCredential:Nt,Error:l}).setInstantiationMode("LAZY").setMultipleInstances(!1)),e.registerVersion("@firebase/auth-compat","0.6.0")}.apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-auth.js - be sure to load firebase-app.js first.")}});//# sourceMappingURL=firebase-auth.js.map
